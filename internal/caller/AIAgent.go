package caller

import (
	"AgenticAI-Client/internal/constant"
	"bufio"
	"context"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"io"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
)

type TuriCore struct {
	ctx     context.Context
	cfg     *AIAgentServerConfig
	Env     []string `json:"env"`
	Arg     []string `json:"arg"`
	Path    string   `json:"path"`
	logger  *zap.Logger
	command *exec.Cmd
	wg      sync.WaitGroup
}

func NewTuriCore(ctx context.Context, c *AIAgentServerConfig) (*TuriCore, error) {
	cli := TuriCore{
		ctx:    ctx,
		cfg:    c,
		Env:    buildEnv(c),
		Arg:    buildArg(c),
		Path:   c.Path,
		logger: zap.L().With(zap.String("module", constant.AIAgentName)),
	}
	cmd := exec.CommandContext(ctx, cli.Path, cli.Arg...)
	cmd.Env = os.Environ()

	// Set the command's standard output to the zap logger
	cli.command = cmd

	return &cli, nil
}

// Run 启动AI agent grpc服务端
func (p *TuriCore) Run() {
	// 获取 stdout 和 stderr 管道
	stdoutPipe, err := p.command.StdoutPipe()
	if err != nil {
		p.logger.Fatal("failed to get stdout pipe", zap.Error(err))
		return
	}
	stderrPipe, err := p.command.StderrPipe()
	if err != nil {
		p.logger.Fatal("failed to get stderr pipe", zap.Error(err))
		return
	}

	// Run the command and capture the output
	err = p.command.Start()
	if err != nil {
		p.logger.Fatal("failed to start AI agent server", zap.Error(err))
	}

	p.logger.Info("turix-core has started", zap.Int("pid", p.command.Process.Pid), zap.String("host", p.cfg.Host), zap.Int("port", p.cfg.Port))

	// 启动goroutine捕获输出
	p.wg.Add(2)
	go p.captureOutput(stdoutPipe, "stdout", zap.InfoLevel)
	go p.captureOutput(stderrPipe, "stderr", zap.WarnLevel)
}

// Close 关闭AI agent grpc服务端
func (p *TuriCore) Close() {
	// 向子进程发送Kill信号
	cmd := exec.Command("pkill", "-9", "-x", constant.AIAgentName)
	err := cmd.Run()
	if err != nil {
		p.logger.Error("failed to force stop turix-core",
			zap.Error(err))
		return
	}
	// 等待子进程退出
	_ = p.command.Wait()

	p.logger.Info("turix-core has stopped gracefully")
	_ = p.logger.Sync()
}

func buildEnv(c *AIAgentServerConfig) (res []string) {
	res = appendIf(res, "test_env", strconv.FormatBool(c.TestEnv))

	return
}

func buildArg(c *AIAgentServerConfig) (res []string) {
	//res = append(res, "--quiet")
	//res = append(res, "--json")
	//res = appendIf(res, "--test_arg", c.TestArg)
	res = appendIf(res, "--port", strconv.Itoa(c.Port))

	return
}

func appendIf(input []string, key, value string) []string {
	if key != "" && value != "" {
		input = append(input, strings.Join([]string{key, value}, "="))
	} else if key != "" {
		input = append(input, key)
	}
	return input
}

func (p *TuriCore) captureOutput(reader io.Reader, stream string, level zapcore.Level) {
	defer p.wg.Done()

	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		msg := scanner.Text()
		switch level {
		case zap.InfoLevel:
			p.logger.Info("turix-core output",
				zap.String("stream", stream),
				zap.String("message", msg),
			)
		case zap.WarnLevel:
			p.logger.Warn("turix-core output",
				zap.String("stream", stream),
				zap.String("message", msg),
			)
		}
	}

	if err := scanner.Err(); err != nil {
		p.logger.Error("Error reading output",
			zap.String("stream", stream),
			zap.Error(err),
		)
	}
}
