// Package caller -----------------------------
// @file      : AgenticAIClient.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 14:22
// -------------------------------------------
package caller

import (
	"AgenticAI-Client/pkg/utils"
	"errors"
	"os"
	"strconv"
)

type AIAgentServerConfig struct {
	Path    string `yaml:"path"`
	Host    string `yaml:"host"`
	Port    int    `yaml:"port"`
	TestEnv bool   `yaml:"test_env"`
	TestArg string `yaml:"test_arg"`
}

func (cfg *AIAgentServerConfig) Complete() []error {
	var ErrList []error
	if cfg == nil {
		ErrList = append(ErrList, errors.New("AI agent server config is nil"))
		return ErrList
	}

	// 优先读取环境变量
	port, err := strconv.Atoi(os.Getenv("AGENT_PORT"))
	if err != nil || port < 10000 {
		port, err = utils.FindFreePort(cfg.Port)
		if err != nil {
			ErrList = append(ErrList, err)
		}
	}
	cfg.Port = port

	return ErrList
}
