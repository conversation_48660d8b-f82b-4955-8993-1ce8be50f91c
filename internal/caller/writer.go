// Package caller -----------------------------
// @file      : writer.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 09:54
// -------------------------------------------
package caller

import (
	"bytes"

	"go.uber.org/zap"
)

// ZapWriter 是一个实现了 io.Writer 接口的结构体，用于将数据写入 zap.Logger
type ZapWriter struct {
	logger *zap.Logger
}

func NewZapWriter() *ZapWriter {
	w := &ZapWriter{
		logger: zap.L().With(zap.String("module", "turix-core")),
	}
	return w
}

// Write 实现了 io.Writer 接口，将数据写入 zap.Logger
func (zw *ZapWriter) Write(p []byte) (n int, err error) {
	// 去除可能存在的换行符
	line := bytes.TrimRight(p, "\n")
	zw.logger.Info(string(line))
	return len(p), nil
}
