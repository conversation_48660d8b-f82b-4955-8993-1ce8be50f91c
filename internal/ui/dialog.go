// Package ui -----------------------------
// @file      : dialog.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/9 09:55
// -------------------------------------------
package ui

import (
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/internal/ui/task"
	"github.com/wailsapp/wails/v3/pkg/application"
)

var dialogs *Dialogs

func RegisterDialogs(app *application.App) {
	dialogs = NewDialogs(app)
}

func GetDialogs() *Dialogs {
	if dialogs == nil {
		panic("Dialogs not initialized")
	}
	return dialogs
}

type Dialogs struct {
	app               *application.App
	exitWarningDialog *task.ExitWarningDialog
}

func NewDialogs(app *application.App) *Dialogs {
	_exitWarningDialog := task.NewExitWarningDialog(app)
	_dialogs := &Dialogs{
		app:               app,
		exitWarningDialog: _exitWarningDialog,
	}
	return _dialogs
}

func (d *Dialogs) HandleDialogEvent(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	val, ok := vals[0].(event.DialogStatus)
	if !ok {
		return
	}

	switch val {
	case event.ShowExitWarningDialog, event.ShowExitWarningDialogFromLogout, event.ShowExitWarningDialogFromQuit:
		d.exitWarningDialog.SetStatus(val)
		d.exitWarningDialog.Show()
	default:
		return
	}
}
