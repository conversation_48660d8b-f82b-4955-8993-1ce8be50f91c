//go:build darwin

package ui

///*
//#include <CoreGraphics/CoreGraphics.h>
//
//extern CGEventRef callback(CGEventTapProxy proxy, CGEventType type, CGEventRef event, void* refcon);
//*/
//import "C"
//import (
//	"context"
//	"unsafe"
//)
//
//func startGlobalMouseListener(ctx context.Context) {
//	eventMask := C.CGEventMaskBit(C.kCGEventMouseMoved)
//	tap := C.CGEventTapCreate(
//		C.kCGSessionEventTap,
//		C.kCGHeadInsertEventTap,
//		C.kCGEventTapOptionDefault,
//		eventMask,
//		(C.CGEventTapCallBack)(unsafe.Pointer(C.callback)),
//		nil,
//	)
//
//	runLoopSource := C.CFMachPortCreateRunLoopSource(C.kCFAllocatorDefault, tap, 0)
//	C.CFRunLoopAddSource(C.CFRunLoopGetCurrent(), runLoopSource, C.kCFRunLoopCommonModes)
//	C.CGEventTapEnable(tap, true)
//	C.CFRunLoopRun()
//}
//
////export callback
//func callback(proxy C.CGEventTapProxy, eventType C.CGEventType, event C.CGEventRef, refcon unsafe.Pointer) C.CGEventRef {
//	if eventType == C.kCGEventMouseMoved {
//		pt := C.CGEventGetLocation(event)
//		event.EmitEvent("globalMouse", map[string]interface{}{
//			"x": float64(pt.x),
//			"y": float64(pt.y),
//		})
//	}
//	return event
//}
