// Package ui -----------------------------
// @file      : menu.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/18 14:09
// -------------------------------------------
package ui

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/global"
	"AgenticAI-Client/internal/ui/task"
	"github.com/wailsapp/wails/v3/pkg/application"
	"github.com/wailsapp/wails/v3/pkg/icons"
	"runtime"
)

var mainMenu *MainMenu

type MainMenu struct {
	app        *application.App
	menu       *application.Menu
	systemTray *application.SystemTray
	taskMenu   *task.Menu
}

func RegisterSystemTray(app *application.App) {
	systemTray := app.SystemTray.New()
	if runtime.GOOS == "darwin" {
		systemTray.SetTemplateIcon(icons.SystrayMacTemplate)
	}
	mainMenu = NewMainMenu(app, systemTray)
	systemTray.SetMenu(mainMenu.taskMenu.TaskMenu())
	systemTray.SetIcon(global.ReadAssets(constant.LogoPath))
	//app.SetMenu(mainMenu.menu)
}

func NewMainMenu(app *application.App, st *application.SystemTray) *MainMenu {
	// 主菜单容器
	mm := &MainMenu{
		app:        app,
		systemTray: st,
		taskMenu:   task.NewTaskMenu(app, st),
		menu:       app.NewMenu(),
	}

	return mm
}

func (m *MainMenu) TaskMenu() *task.Menu {
	return m.taskMenu
}

func GetMainMenu() *MainMenu {
	if mainMenu == nil {
		panic("mainMenu is nil")
	}
	return mainMenu
}
