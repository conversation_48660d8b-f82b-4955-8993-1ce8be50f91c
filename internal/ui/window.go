// Package ui -----------------------------
// @file      : backdrop-transparent-window.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/23 14:26
// -------------------------------------------
package ui

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/pkg/service"
	"github.com/wailsapp/wails/v3/pkg/application"
	"github.com/wailsapp/wails/v3/pkg/events"
)

var windows *Windows

func RegisterWindows(app *application.App) {
	windows = NewWindows(app)
}

func GetWindows() *Windows {
	if windows == nil {
		panic("windows is nil")
	}
	return windows
}

type Windows struct {
	app            *application.App
	loginWindow    *application.WebviewWindow
	btWindow       *application.WebviewWindow
	floatingWindow *application.WebviewWindow
}

func NewWindows(app *application.App) *Windows {
	_loginWindow := app.Window.NewWithOptions(application.WebviewWindowOptions{
		Name:            constant.WindowLogin,
		Title:           constant.APPName,
		InitialPosition: application.WindowCentered,
		Mac: application.MacWindow{
			InvisibleTitleBarHeight: 50,
			Backdrop:                application.MacBackdropTranslucent,
			TitleBar:                application.MacTitleBarHiddenInset,
		},
		BackgroundColour: application.NewRGBA(27, 38, 54, 1),
		URL:              service.GetCloudUIURL(event.WindowShowHome),
		Width:            1200,
		Height:           900,
		DevToolsEnabled:  true,
		KeyBindings: map[string]func(window *application.WebviewWindow){
			"cmdorctrl+optionoralt+f12": func(window *application.WebviewWindow) {
				window.OpenDevTools()
			},
		},
	})

	_floatBallWindow := app.Window.NewWithOptions(application.WebviewWindowOptions{
		Name:                       constant.WindowFloat,
		Title:                      "FloatingWindow",
		URL:                        service.GetCloudUIURL(event.WindowShowFloating),
		StartState:                 application.WindowStateNormal,
		InitialPosition:            application.WindowXY,
		DefaultContextMenuDisabled: true,
		Frameless:                  true,
		AlwaysOnTop:                true,
		DisableResize:              true,
		Mac: application.MacWindow{
			Backdrop: application.MacBackdropTransparent,
			TitleBar: application.MacTitleBarHiddenInsetUnified,
			WebviewPreferences: application.MacWebviewPreferences{
				VibrancyEnabled: application.Enabled,
			},
		},
		KeyBindings: map[string]func(window *application.WebviewWindow){
			"cmdorctrl+space": func(window *application.WebviewWindow) {
				window.OpenDevTools()
			},
		},
	})

	_btWindow := app.Window.NewWithOptions(application.WebviewWindowOptions{
		Name:                       constant.WindowBackdropTransparent,
		Title:                      "TransparentWindow",
		URL:                        constant.InnerUIBackdropTransparentPage,
		StartState:                 application.WindowStateMaximised,
		BackgroundType:             application.BackgroundTypeTranslucent,
		InitialPosition:            application.WindowXY,
		DefaultContextMenuDisabled: true,
		Frameless:                  true,
		BackgroundColour:           application.NewRGBA(0, 0, 0, 0),
		AlwaysOnTop:                true,
		Mac: application.MacWindow{
			Backdrop: application.MacBackdropTransparent,
			TitleBar: application.MacTitleBarHiddenInsetUnified,
		},
		IgnoreMouseEvents: true,
	})

	_windows := &Windows{
		app:            app,
		loginWindow:    _loginWindow,
		floatingWindow: _floatBallWindow,
		btWindow:       _btWindow,
	}
	return _windows
}

func (w *Windows) RegisterEvents() func() {
	lw, btw, flw := w.loginWindow, w.btWindow, w.floatingWindow
	return func() {
		lw.RegisterHook(events.Common.WindowClosing, func(e *application.WindowEvent) {
			lw.Hide()
			e.Cancel()
		})

		w.app.Event.OnApplicationEvent(events.Windows.ApplicationStarted, func(e *application.ApplicationEvent) {
			btw.Hide()
			lw.Hide()
		})

		w.app.Event.OnApplicationEvent(events.Mac.ApplicationShouldHandleReopen, func(e *application.ApplicationEvent) {
			w.checkLoginStatus()
		})

		// hide the window temporarily
		lw.Hide()
		btw.Hide()
		flw.Hide()

		w.checkLoginStatus()
	}
}

func (w *Windows) checkLoginStatus() {
	if !service.UserServiceImpl.IsLogin() {
		uid := service.UserServiceImpl.GetUserID()
		_, oidc := service.UserServiceImpl.GetUserInfo()
		if len(oidc.IDToken) == 0 || len(uid) == 0 {
			event.EmitEvent(constant.EventLogin, true)
			return
		}

		// if user have logged in, the login window will not be displayed
		event.EmitEvent(constant.EventLogin, true)
	}
}

func (w *Windows) HandleMainWindowEvents(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	val, ok := vals[0].(event.WindowStatus)
	if !ok {
		// from inner UI
		// TypeScript number to Go int
		_val, _ok := vals[0].(float64)
		if !_ok {
			return
		}
		val = event.WindowStatus(_val)
	}

	if val == event.WindowHide {
		w.loginWindow.SetURL("")
		w.loginWindow.Hide()
		return
	}

	if val == event.WindowShowCustomizeUrl {
		// load customize url into window
		if len(vals) < 2 {
			return
		}
		url, _ok := vals[1].(string)
		if !_ok {
			return
		}
		url = service.GetCloudUIURLWithCustomized(val, url, "")
		w.loginWindow.SetURL(url)
		w.loginWindow.Show()
		return
	} else if val == event.WindowShowSetting && len(vals) >= 2 {
		// load device setting url into window
		query, _ok := vals[1].(string)
		if !_ok {
			return
		}
		url := service.GetCloudUIURLWithCustomized(val, "", query)
		w.loginWindow.SetURL(url)
		w.loginWindow.Show()
		return
	}

	url := service.GetCloudUIURL(val)
	w.loginWindow.SetURL(url)
	w.loginWindow.Show()
	w.loginWindow.Focus()
}
