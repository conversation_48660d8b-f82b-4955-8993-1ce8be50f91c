//go:build windows

package ui

import (
	"AgenticAI-Client/internal/event"
	"context"
	"syscall"
	"unsafe"
)

var (
	user32              = syscall.NewLazyDLL("user32.dll")
	setWindowsHookEx    = user32.NewProc("SetWindowsHookExW")
	callNextHookEx      = user32.NewProc("CallNextHookEx")
	unhookWindowsHookEx = user32.NewProc("UnhookWindowsHookEx") // 新增卸载函数
	getCursorPos        = user32.NewProc("GetCursorPos")
)

const (
	WH_MOUSE_LL  = 14
	WM_MOUSEMOVE = 0x0200
)

type POINT struct{ X, Y int32 }

func startGlobalMouseListener(ctx context.Context) {
	// 声明变量并忽略编译警告（关键修正）
	var hook uintptr
	hook, _, _ = setWindowsHookEx.Call(
		WH_MOUSE_LL,
		syscall.NewCallback(func(nCode int, wParam uintptr, lParam uintptr) uintptr {
			if wParam == WM_MOUSEMOVE {
				var pt POINT
				getCursorPos.Call(uintptr(unsafe.Pointer(&pt)))
				event.EmitEvent("globalMouse", map[string]interface{}{
					"x": pt.X,
					"y": pt.Y,
				})
			}
			result, _, _ := callNextHookEx.Call(0, uintptr(nCode), wParam, lParam)
			return result
		}),
		0, 0,
	)

	// 保持钩子存活（新增卸载逻辑）
	defer unhookWindowsHookEx.Call(hook)
	select {} // 阻止函数退出
}
