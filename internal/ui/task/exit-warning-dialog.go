// Package task -----------------------------
// @file      : exit-warning-dialog.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/9 10:11
// -------------------------------------------
package task

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"github.com/wailsapp/wails/v3/pkg/application"
)

type ExitWarningDialog struct {
	app           *application.App
	dialog        *application.MessageDialog
	cancelBtn     *application.Button
	confirmBtn    *application.Button
	currentStatus event.DialogStatus
}

func NewExitWarningDialog(app *application.App) *ExitWarningDialog {
	_exitWarningDialog := application.WarningDialog().
		SetTitle(constant.ExitWarningDialogTitle).
		SetMessage(constant.ExitWarningDialogMessage)

	_dialog := &ExitWarningDialog{
		app:    app,
		dialog: _exitWarningDialog,
	}

	_dialog.cancelBtn = _exitWarningDialog.AddButton(constant.ExitWarningDialogLeftBtnMsg).OnClick(func() {
	})
	_dialog.confirmBtn = _exitWarningDialog.AddButton(constant.ExitWarningDialogRightBtnMsg).OnClick(func() {
		if _dialog.currentStatus == event.ShowExitWarningDialogFromLogout {
			event.EmitEvent(constant.EventLogin, false)
		}
		if _dialog.currentStatus == event.ShowExitWarningDialogFromQuit {
			event.EmitEvent(constant.EventTaskChatHandle, StatusQuit)
			return
		}
	})

	_dialog.dialog.SetDefaultButton(_dialog.confirmBtn)

	return _dialog
}

func (ewd *ExitWarningDialog) Show() {
	if ewd == nil || ewd.dialog == nil {
		return
	}
	ewd.dialog.Show()
}

func (ewd *ExitWarningDialog) SetStatus(status event.DialogStatus) {
	ewd.currentStatus = status
}
