// Package constant -----------------------------
// @file      : constant.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/11 15:28
// -------------------------------------------
package constant

const (
	APPName     = "TuriX"
	AIAgentName = "turix-core"

	// server
	DefaultServerAddr              = "0.0.0.0"
	DefaultServerPort              = 10682
	DefaultServerRedirectUrl       = "http://localhost:%d/auth/login"
	DefaultServerRedirectLogoutUrl = "http://localhost:%d/auth/logout"

	// dir
	DefaultLogDir  = "/tmp/log"
	DefaultDataDir = "/var/lib/TuriX"

	// log
	LogMaxSizeMegaBytes = 100
	LogMaxAgeDay        = 7
	LogBackupCount      = 7

	// AI agent rpc server
	DefaultAIAgentRPCHost = "localhost"
	DefaultAIAgentRPCPort = 10010

	// DefaultClientID 区分客户端的设备码
	DefaultClientID = "go_client" // 已不再使用MQTT

	// MQTT相关常量已移除，改用Wails event系统

	// ui domain
	CloudWebUIURL    = "https://cloud.dev.turix.ai"
	CloudWebUIURLDev = "https://cloud.dev.turix.ai"

	// web ui
	CloudWebUITaskURL = "/agent/main"
	CloudWebUIHomeURL = "/agent/chat"

	// inner url
	InnerUISettingURL              = "/#/SettingLayout"
	InnerUIBackdropTransparentPage = "/#/ActiveControlBorder"
	InnerUIFloatingPage            = "/float/input"
	InnerUIFloatBallChatDialog     = "/#/ChatMain"

	// event
	EventSystemTrayMenuChange = "event.systemTrayMenuChange"
	EventTaskChatHandle       = "event.task.chat"
	EventDialog               = "event.dialog"
	EventMainWindow           = "event.mainWindow"
	EventLogin                = "event.login"
	EventPermission           = "event.permission"

	EventInnerUI                    = "event.inner.ui"
	EventInnerUIToggleBall          = "event.toggleBall"
	EventInnerUIRunTask             = "event.runTask"
	EventInnerUIMouseMoveGlobal     = "event.mouseMoveGlobal"
	EventInnerUIStopMouseMoveGlobal = "event.stopMouseMoveGlobal"

	// EventBus events (替代MQTT)
	EventChatResponse    = "event.chat.response"
	EventDeviceStatus    = "event.device.status"
	EventDeviceHeartbeat = "event.device.heartbeat"

	// asset
	LogoPath           = "frontend/dist/design/turi-agentic-logo-b.svg"
	EndPic             = "frontend/dist/design/jieshu.svg"
	EndHoverPic        = "frontend/dist/design/jieshu-hover.svg"
	RunningHoverPic    = "frontend/dist/design/jinxingzhong-hover.svg"
	RunningPic         = "frontend/dist/design/jixingzhong.svg"
	ResumePic          = "frontend/dist/design/jixu.svg"
	ResumeHoverPic     = "frontend/dist/design/jixu-hover.svg"
	PendingPic         = "frontend/dist/design/kongxian.svg"
	PendingHoverPic    = "frontend/dist/design/kongxian-hover.svg"
	SettingPic         = "frontend/dist/design/shezhi.svg"
	SettingHoverPic    = "frontend/dist/design/shezhi-hover.svg"
	LogoutPic          = "frontend/dist/design/tuichu.svg"
	LogoutHoverPic     = "frontend/dist/design/tuichu-hover.svg"
	ErrorPic           = "frontend/dist/design/yichang.svg"
	ErrorHoverPic      = "frontend/dist/design/yichang-hover.svg"
	StopPic            = "frontend/dist/design/zhanting.svg"
	StopHoverPic       = "frontend/dist/design/zhanting-hover.svg"
	HomePic            = "frontend/dist/design/zhuye.svg"
	HomeHoverPic       = "frontend/dist/design/zhuye-hover.svg"
	NewTaskPic         = "frontend/dist/design/xinrenwu.svg"
	NewTaskHoverPic    = "frontend/dist/design/xinrenwu-hover.svg"
	ExitPic            = "frontend/dist/design/tuichu_turi.svg"
	AccountPic         = "frontend/dist/design/zhanghu.svg"
	FloatWindowHidePic = "frontend/dist/design/icon-yincang.svg"
	FloatWindowShowPic = "frontend/dist/design/icon-zhanshi.svg"

	// oss
	DefaultOSSEndpoint        = "oss-cn-hongkong.aliyuncs.com"
	DefaultOSSAccessKeyID     = "LTAI5tSLrcPiiqxY8h9BgDVy"
	DefaultOSSAccessKeySecret = "******************************"
	DefaultOSSBucketName      = "ai-formaloss"
	DefaultOSSUrlPrefix       = "https://ai-formaloss.oss-cn-hongkong.aliyuncs.com"
	DefaultOSSTimeout         = 60
	DefaultOSSExpireTime      = 3600

	// dialogs
	ExitWarningDialogTitle       = "注意"
	ExitWarningDialogMessage     = "任务未完成，退出会结束任务"
	ExitWarningDialogLeftBtnMsg  = "取消"
	ExitWarningDialogRightBtnMsg = "确定"

	// updater
	UpdaterManifestUrl = "https://ai-formaloss.oss-cn-hongkong.aliyuncs.com/release/%s/%s.json"
	UpdaterBinaryUrl   = "https://ai-formaloss.oss-cn-hongkong.aliyuncs.com/release/%s"
	UpdaterDiffUrl     = "https://ai-formaloss.oss-cn-hongkong.aliyuncs.com/release/%s"

	// scanner
	ScanLoopFloorLimit                 = 5
	ScanHeartbeatDefaultInterval       = "15s"
	ScanPermissionCheckDefaultInterval = "10s"
	ScanGetMousePosDefaultInterval     = "10ms"

	// window
	WindowLogin               = "login-window"
	WindowFloat               = "float-window"
	WindowBackdropTransparent = "backdrop-transparent-window"
)
