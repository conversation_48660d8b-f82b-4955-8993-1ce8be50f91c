// Package app -----------------------------
// @file      : app.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/29 11:17
// -------------------------------------------
package app

import (
	"AgenticAI-Client/internal/caller"
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/internal/global"
	"AgenticAI-Client/internal/scanner"
	"AgenticAI-Client/internal/ui"
	"AgenticAI-Client/pkg/apiserver"
	"AgenticAI-Client/pkg/client"
	"AgenticAI-Client/pkg/eventbus"
	"AgenticAI-Client/pkg/oss"
	"AgenticAI-Client/pkg/service"
	"context"
	"embed"
	"fmt"

	"github.com/wailsapp/wails/v3/pkg/application"
	"go.uber.org/zap"
)

type TuriXApp struct {
	ctx        context.Context
	wails      *application.App
	cancelFunc context.CancelFunc
	cfg        *config.TuriXConfig

	aiAgentClient *client.AIClient
	turiCore      *caller.TuriCore
	eventBus      eventbus.EventBus
	server        apiserver.Interface
	oc            *oss.Client
	scanner       *scanner.Impl
	userSrv       *service.UserService
	permissionSrv *service.PermissionService
	menu          *ui.MainMenu
	windows       *ui.Windows
	dialogs       *ui.Dialogs
}

func New(assets embed.FS) *TuriXApp {
	ctx, cancel := context.WithCancel(context.Background())

	// 获取配置
	c := config.GetTuriXConfig()

	// load global assets
	err := global.LoadAssets(assets)
	if err != nil {
		zap.L().Fatal("Failed to read assets", zap.Error(err))
	}
	// 初始化AI agent grpc 服务端
	agent, err := caller.NewTuriCore(ctx, &c.AIAgentServer)
	if err != nil {
		zap.L().Fatal("Failed to create ai agent server", zap.Error(err))
	}

	_eventBus := eventbus.NewEventBus(ctx)

	// Create wails app
	wApp := application.New(application.Options{
		Name:        constant.APPName,
		Description: "我是TuriX，很高兴见到你",
		Assets: application.AssetOptions{
			Handler: application.AssetFileServerFS(assets),
		},
		Mac: application.MacOptions{
			ApplicationShouldTerminateAfterLastWindowClosed: false,
		},
	})

	// init service
	_permissionSrv := service.NewPermissionService(c)
	_userSrv := service.NewUserService(c, _eventBus, wApp, _permissionSrv)

	// init api server
	srv, err := apiserver.New(ctx, &c.Server, _userSrv, _permissionSrv)
	if err != nil {
		zap.L().Fatal("Failed to create api server", zap.Error(err))
	}

	// Register service
	wApp.RegisterService(application.NewService(_userSrv))
	wApp.RegisterService(application.NewService(_permissionSrv))

	// Register the menu and system tray
	ui.RegisterSystemTray(wApp)

	// Register the windows
	ui.RegisterWindows(wApp)

	// Register the dialogs
	ui.RegisterDialogs(wApp)

	// init oss client
	_oc, err := oss.NewClient(&c.OSS)
	if err != nil {
		zap.L().Fatal("Failed to create OSS client", zap.Error(err))
	}

	// init AI agent client
	ac, err := client.NewAIClient(ctx, c, _eventBus, _oc)
	if err != nil {
		zap.L().Fatal("Failed to create AI client", zap.Error(err))
	}

	// init scanner
	_scanner := scanner.New(ctx, &c.Scanner, _eventBus, _permissionSrv)

	_app := &TuriXApp{
		ctx:           ctx,
		cfg:           c,
		wails:         wApp,
		cancelFunc:    cancel,
		eventBus:      _eventBus,
		aiAgentClient: ac,
		turiCore:      agent,
		server:        srv,
		userSrv:       _userSrv,
		permissionSrv: _permissionSrv,
		oc:            _oc,
		scanner:       _scanner,
		menu:          ui.GetMainMenu(),
		windows:       ui.GetWindows(),
		dialogs:       ui.GetDialogs(),
	}

	// 将TuriXApp绑定到Wails，这样前端就可以调用其公开方法
	appService := application.NewService(_app)
	wApp.RegisterService(appService)

	err = _app.BeforeRunHandler()
	if err != nil {
		zap.L().Fatal("Failed to run before run handler", zap.Error(err))
	}

	return _app
}

// ========== 前端可调用的方法 ==========

// SendChatMessage 前端发送聊天消息到后端
func (a *TuriXApp) SendChatMessage(message string) error {
	// 这里可以处理前端发送的聊天消息
	zap.L().Info("收到前端聊天消息", zap.String("message", message))

	// 可以调用AI客户端处理消息
	// 或者通过EventBus发送到其他组件
	a.aiAgentClient.ProcessMessage(message)

	return nil
}

// GetDeviceInfo 获取设备信息
func (a *TuriXApp) GetDeviceInfo() map[string]interface{} {
	return map[string]interface{}{
		"deviceID": a.userSrv.GetDeviceID(),
		"isLogin":  a.userSrv.IsLogin(),
		"userID":   a.userSrv.GetUserID(),
	}
}

// UpdateSettings 更新应用设置
func (a *TuriXApp) UpdateSettings(settings map[string]interface{}) error {
	zap.L().Info("收到前端设置更新", zap.Any("settings", settings))

	// 这里可以处理设置更新逻辑
	// 例如：a.cfg.UpdateSettings(settings)

	return nil
}

// TriggerTask 触发任务执行
func (a *TuriXApp) TriggerTask(taskData map[string]interface{}) error {
	zap.L().Info("收到前端任务触发请求", zap.Any("taskData", taskData))

	// 这里可以处理任务触发逻辑
	// 例如：a.aiAgentClient.ExecuteTask(taskData)
	a.aiAgentClient.ExecuteTask(taskData)

	return nil
}

// BeforeRunHandler 应用启动前处理函数
func (a *TuriXApp) BeforeRunHandler() error {
	// 启动前，检查是否登录，如果登录了，则执行以下钩子动作
	if a.userSrv.IsLogin() {
		// EventBus不需要重连，移除此逻辑
		zap.L().Debug("EventBus does not require reconnection")

		// 发送设备绑定消息
		err := a.userSrv.PublishDeviceBinding(true)
		if err != nil {
			return fmt.Errorf("publish device binding failed with %w", err)
		}
		event.EmitEvent(constant.EventMainWindow, event.WindowShowHome)
	}

	a.RegisterEventsHandler()
	a.RegisterCallbackFunction()

	return nil
}

func (a *TuriXApp) Run() {
	// 启动eventbus
	go a.eventBus.Run()

	// 启动ai agent
	a.turiCore.Run()

	// 绑定退出回调
	a.wails.OnShutdown(func() {
		a.Shutdown()
	})

	// Run the api server
	go a.server.Run()

	// Run the scanner
	a.scanner.Run()

	// Run the application. This blocks until the application has been exited.
	err := a.wails.Run()
	// If an error occurred while running the application, log it and exit.
	if err != nil {
		zap.L().Fatal("", zap.Error(err))
	}
}

// Shutdown A callback for when the application is about to quit.
func (a *TuriXApp) Shutdown() {
	a.cancelFunc()
	a.eventBus.Close()
	a.aiAgentClient.Close()
	a.turiCore.Close()
	a.scanner.Close()
}
