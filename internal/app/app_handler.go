// Package app -----------------------------
// @file      : app_handler.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/29 13:08
// -------------------------------------------
package app

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"fmt"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
	"go.uber.org/zap"
)

// RegisterEventsHandler 注册app事件处理器
func (a *TuriXApp) RegisterEventsHandler() {
	event.RegisterEvent(a.wails)

	// 注册内置前端(inner UI)事件处理器
	event.OnEvent(constant.EventInnerUI, a.innerUIEventsHandler)

	// 注册用户服务事件处理器
	event.OnEvent(constant.EventLogin, a.userSrv.HandleLoginEvent)

	// 注册AI Agent事件处理器
	event.OnEvent(constant.EventTaskChatHandle, a.aiAgentClient.HandleChatRequest)
	event.OnEvent(constant.EventPermission, a.aiAgentClient.HandlePermissionRequest)

	// 注册菜单事件处理器
	event.OnEvent(constant.EventSystemTrayMenuChange, a.menu.TaskMenu().HandleTaskEvent)

	// 注册窗口事件处理器
	a.windows.RegisterEvents()()
	event.OnEvent(constant.EventMainWindow, a.windows.HandleMainWindowEvents)

	// 注册对话框事件处理器
	event.OnEvent(constant.EventDialog, a.dialogs.HandleDialogEvent)
}

// RegisterCallbackFunction 注册回调函数
func (a *TuriXApp) RegisterCallbackFunction() {

	// 注册eventbus回调函数
	a.eventBus.RegisterSettingRequestHandler(a.userSrv.HandleDeviceSettingRequest)
	a.eventBus.RegisterConnectHandler(a.aiAgentClient.HandleConnectHandler)
	a.eventBus.RegisterChatRequestHandler(a.aiAgentClient.HandleBrokerChatRequest)
	a.eventBus.RegisterQuitAppHandler(a.quitAppCallbackFunc)

	// 注册菜单回调函数
	a.menu.TaskMenu().SetQuitAppCallbackFunc(a.quitAppMenuCallbackFunc)
}

func (a *TuriXApp) innerUIEventsHandler(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	_val, _ok := vals[0].(float64)
	if !_ok {
		return
	}
	val := event.InnerUIEvent(_val)

	switch val {
	case event.InnerUIEventOpenFloatBall, event.InnerUIEventHideFloatBall:
		a.menu.TaskMenu().ToggleFloatBall()
	case event.InnerUIEventLogout:
		a.menu.TaskMenu().Logout()
	//case event.InnerUIEventOpenFloatBallChatMain:
	//	query := fmt.Sprintf("task_id=%s", a.aiAgentClient.GetTaskID())
	//	url := service.GetCloudUIURLWithCustomized(event.WindowShowFloatBallChatMain, "", query)
	//	a.windows.FloatBallChatMainWindow().Window().SetURL(url)
	//	a.windows.FloatBallChatMainWindow().Window().Show()
	//case event.InnerUIEventHideFloatBallChatMain:
	//	a.windows.FloatBallChatMainWindow().Window().Hide()
	case event.InnerUIEventStartScanMouseMoveGlobalLocation:
		a.scanner.InnerUIReporter().SetDisabled(false)
	case event.InnerUIEventStopScanMouseMoveGlobalLocation:
		a.scanner.InnerUIReporter().SetDisabled(true)
	default:
		zap.L().Error("unknown event", zap.Any("event", val))
	}
}

// handleFloatBallMainChatRunTaskEvent 悬浮球对话框窗口展示处理函数。对话框展示时，向内嵌ui发送任务开始运行的消息
func (a *TuriXApp) handleFloatBallMainChatRunTaskEvent(e *application.WindowEvent) {
	taskID := a.aiAgentClient.GetTaskID()
	if len(taskID) == 0 {
		return
	}
	time.Sleep(500 * time.Microsecond)
	event.EmitInnerUIEvent(constant.EventInnerUIRunTask, taskID)
}

func (a *TuriXApp) quitAppMenuCallbackFunc(c *application.Context) {
	if !a.menu.TaskMenu().GetPause() {
		event.EmitEvent(constant.EventDialog, event.ShowExitWarningDialogFromQuit)
		return
	}

	err := a.quitAppCallbackFunc()
	if err != nil {
		zap.L().Error("quit app failed", zap.Error(err))
	}

}

func (a *TuriXApp) quitAppCallbackFunc() error {
	// 发送设备解绑消息
	err := a.userSrv.PublishDeviceBinding(false)
	if err != nil {
		return fmt.Errorf("publish device binding failed with %w", err)
	}
	a.wails.Quit()

	return nil
}
