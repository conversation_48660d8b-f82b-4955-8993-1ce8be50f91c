// Package scanner -----------------------------
// @file      : scanner.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 16:53
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/apiserver/handler"
	"AgenticAI-Client/pkg/broker"
	"context"
)

type Impl struct {
	ctx     context.Context
	cancel  context.CancelCauseFunc
	c       *config.ScannerConfig
	hbp     *HeartbeatReporter
	pmc     *PermissionCheck
	innerUI *InnerUIReporter
}

func New(ctx context.Context, c *config.ScannerConfig, _broker broker.Interface, _permission handler.PermissionServiceHandler) *Impl {
	ctx, cancel := context.WithCancelCause(ctx)
	_hbp := NewHeartbeatReporter(ctx, c, _broker)
	_pmc := NewPermissionCheck(ctx, c, _permission)
	_innerUI := NewInnerUIReporter(ctx, c)
	impl := &Impl{
		ctx:     ctx,
		cancel:  cancel,
		c:       c,
		hbp:     _hbp,
		pmc:     _pmc,
		innerUI: _innerUI,
	}

	return impl
}

func (p *Impl) Run() {
	go p.hbp.scan()
	go p.pmc.scan()
	go p.innerUI.scan()
}

func (p *Impl) Close() {
	p.cancel(nil)
}

func (p *Impl) InnerUIReporter() *InnerUIReporter {
	return p.innerUI
}

func (p *Impl) PermissionCheck() *PermissionCheck {
	return p.pmc
}
