// Package scanner -----------------------------
// @file      : heartbeat_reporter.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 17:37
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/eventbus"
	"AgenticAI-Client/pkg/service"
	"context"
	"time"

	"go.uber.org/zap"
)

type HeartbeatReporter struct {
	ctx      context.Context
	c        *config.HeartbeatConfig
	logger   *zap.Logger
	eventBus eventbus.EventBus
}

func NewHeartbeatReporter(ctx context.Context, cfg *config.ScannerConfig, _eventBus eventbus.EventBus) *HeartbeatReporter {
	_hbp := &HeartbeatReporter{
		ctx:      ctx,
		c:        &cfg.Heartbeat,
		logger:   zap.L().With(zap.String("module", "HeartbeatReporter")),
		eventBus: _eventBus,
	}

	return _hbp
}

func (hbp *HeartbeatReporter) scan() {
	ch := time.NewTicker(hbp.c.ScanLoopIntervalDuration)
	defer ch.Stop()

	for {
		select {
		case <-hbp.ctx.Done():
			hbp.logger.Info("scan loop exit")
			return
		case <-ch.C:
			if hbp.c.Disabled || !service.UserServiceImpl.IsLogin() {
				continue
			}

			// send heartbeat using mqtt broker
			heartbeatData, err := service.UserServiceImpl.GenerateHeartbeatResponse()
			if err != nil {
				hbp.logger.Error("generate heartbeat response failed", zap.Error(err))
				continue
			}
			hbp.eventBus.EmitDeviceHeartbeat(heartbeatData)
			hbp.logger.Debug("Device heartbeat emitted")

			//hbp.logger.Debug("send heartbeat", zap.Any("heartbeat", heartbeatData))
		}
	}
}
