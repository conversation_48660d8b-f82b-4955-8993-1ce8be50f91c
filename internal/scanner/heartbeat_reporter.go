// Package scanner -----------------------------
// @file      : heartbeat_reporter.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 17:37
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/broker"
	"AgenticAI-Client/pkg/service"
	"context"
	"go.uber.org/zap"
	"time"
)

type HeartbeatReporter struct {
	ctx    context.Context
	c      *config.HeartbeatConfig
	logger *zap.Logger
	broker broker.Interface
}

func NewHeartbeatReporter(ctx context.Context, cfg *config.ScannerConfig, _broker broker.Interface) *HeartbeatReporter {
	_hbp := &HeartbeatReporter{
		ctx:    ctx,
		c:      &cfg.Heartbeat,
		logger: zap.L().With(zap.String("module", "HeartbeatReporter")),
		broker: _broker,
	}

	return _hbp
}

func (hbp *HeartbeatReporter) scan() {
	ch := time.NewTicker(hbp.c.ScanLoopIntervalDuration)
	defer ch.Stop()

	for {
		select {
		case <-hbp.ctx.Done():
			hbp.logger.Info("scan loop exit")
			return
		case <-ch.C:
			if hbp.c.Disabled || !service.UserServiceImpl.IsLogin() {
				continue
			}

			// send heartbeat using mqtt broker
			heartbeatData, err := service.UserServiceImpl.GenerateHeartbeatResponse()
			if err != nil {
				hbp.logger.Error("generate heartbeat response failed", zap.Error(err))
				continue
			}
			err = hbp.broker.PublishDeviceHeartbeatReply(heartbeatData)
			if err != nil {
				hbp.logger.Error("publish heartbeat failed", zap.Error(err))
			}

			//hbp.logger.Debug("send heartbeat", zap.Any("heartbeat", heartbeatData))
		}
	}
}
