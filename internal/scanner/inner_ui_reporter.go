// Package scanner -----------------------------
// @file      : inner_ui_reporter.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/6/4 10:00
// -------------------------------------------
package scanner

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"context"
	"github.com/go-vgo/robotgo"
	hook "github.com/robotn/gohook"
	"go.uber.org/zap"
	"time"
)

type InnerUIReporter struct {
	ctx    context.Context
	c      *config.InnerUIReporterConfig
	logger *zap.Logger
}

func NewInnerUIReporter(ctx context.Context, cfg *config.ScannerConfig) *InnerUIReporter {
	_innerUI := &InnerUIReporter{
		ctx:    ctx,
		c:      &cfg.InnerUI,
		logger: zap.L().With(zap.String("module", "InnerUIReporter")),
	}

	return _innerUI
}

func (p *InnerUIReporter) scan() {
	go p.startMouseUpHandler()

	ch := time.NewTicker(p.c.ScanLoopIntervalDuration)
	defer ch.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("scan loop exit")
			return
		case <-ch.C:
			if p.c.Disabled {
				continue
			}
			// 获取全局鼠标位置（相对于主屏幕左上角）
			x, y := robotgo.Location()
			// 通过事件推给前端
			// 前端监听 "mouseMoveGlobal" 事件
			event.EmitInnerUIEvent(constant.EventInnerUIMouseMoveGlobal, map[string]int{
				"x": x,
				"y": y,
			})
		}
	}
}

func (p *InnerUIReporter) startMouseUpHandler() {
	evChan := hook.Start()
	//defer hook.End()

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("startMouseUpHandler loop exit")
			return
		case ev, ok := <-evChan:
			if !ok {
				return
			}
			// 捕获到鼠标左键抬起事件时，中止发送全局鼠标位置给内置UI
			if ev.Button == hook.MouseMap["left"] && (ev.Kind == hook.MouseHold || ev.Kind == hook.MouseUp) {
				p.SetDisabled(true)
				event.EmitInnerUIEvent(constant.EventInnerUIStopMouseMoveGlobal)
			}
		}
	}
}

// SetDisabled 设置是否禁用
func (p *InnerUIReporter) SetDisabled(flag bool) {
	p.c.Disabled = flag
}
