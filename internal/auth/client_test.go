// Package auth -----------------------------
// @file      : client_test.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/19 11:00
// -------------------------------------------
package auth

import (
	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"testing"
)

func TestClient_Logout(t *testing.T) {
	type args struct {
		accessToken string
	}
	tests := []struct {
		name string
		args args
	}{
		// Add test cases.
		{name: "t1", args: args{accessToken: _accessToken}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := _client.Logout(tt.args.accessToken)
			if err != nil {
				t.<PERSON>("LogoutHandler() error = %v", err)
				return
			}
		})
	}
}

func TestClient_GenerateOIDCLogoutURL(t *testing.T) {
	type args struct {
		idToken string
	}
	tests := []struct {
		name string
		args args
	}{
		// Add test cases.
		{name: "t1", args: args{idToken: _idToken}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &authentication.BuildLogoutURLParams{
				IDTokenHint: tt.args.idToken,
			}
			got, err := _client.GenerateOIDCLogoutURL(req)
			if err != nil {
				t.Errorf("GenerateOIDCLogoutURL() error = %v", err)
				return
			}
			t.Logf("GenerateOIDCLogoutURL() = %v", got)
		})
	}
}
