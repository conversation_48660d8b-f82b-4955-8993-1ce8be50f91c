// Package auth -----------------------------
// @file      : error.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/15 11:36
// -------------------------------------------
package auth

type Error struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
	ApiCode    int    `json:"apiCode,omitempty"`
	RequestId  string `json:"requestId,omitempty"`
}

func (e *Error) Error() string {
	return e.Message
}

func NewError(statusCode int, message string, apiCode int, requestID string) *Error {
	if statusCode == 200 {
		return nil
	}
	err := &Error{
		StatusCode: statusCode,
		Message:    message,
		ApiCode:    apiCode,
		RequestId:  requestID,
	}

	return err
}
