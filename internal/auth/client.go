// Package auth -----------------------------
// @file      : client.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/15 10:32
// -------------------------------------------
package auth

import (
	"AgenticAI-Client/internal/config"
	"fmt"
	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"github.com/gofiber/fiber/v3"
	"go.uber.org/zap"
	"sync"
)

const (
	AUTHING_APP_ID                  = "6826d93ce71bd7bb28fce8c8"
	AUTHING_APP_SECRET              = "7669c7036bcdeb83da0e98757cb93481"
	AUTHING_APP_HOST                = "https://turi-ngtechai.authing.cn"
	AUTHING_APP_REDIRECT_URI        = "https://cloud.dev.turix.ai/thirdLogin"
	AUTHING_APP_REDIRECT_LOGOUT_URI = "https://cloud.dev.turix.ai"
	AUTHING_PROTOCOL                = "oidc"
	AUTHING_SCOPE                   = "offline_access openid username address email phone profile roles external_id extended_fields"
)

var _client *Client

type Client struct {
	// acp authentication client pool
	acp     *sync.Pool
	logger  *zap.Logger
	options *authentication.AuthenticationClientOptions
	cfg     *config.TuriXConfig
}

func NewClient(c *config.TuriXConfig) *Client {
	if _client == nil {
		_logger := zap.L().With(zap.String("module", "authing"))
		_options := authentication.AuthenticationClientOptions{
			AppId:                           AUTHING_APP_ID,             // Authing 应用 ID
			AppSecret:                       AUTHING_APP_SECRET,         // Authing 应用密钥
			AppHost:                         AUTHING_APP_HOST,           // Authing 应用域名，如https://example.authing.cn。注意：Host 地址为示例样式，不同版本用户池的应用 Host 地址形式有所差异，实际地址以 自建应用->应用配置->认证配置 下 `认证地址 `字段为准。
			RedirectUri:                     c.Server.RedirectUrl,       // Authing 应用配置的登录回调地址
			LogoutRedirectUri:               c.Server.RedirectLogoutUrl, // Authing 应用配置的登出回调地址
			Protocol:                        AUTHING_PROTOCOL,           // 应用协议类型，默认为 oidc。可选值为 oidc、oauth、cas、saml
			IntrospectionEndPointAuthMethod: authentication.None,
			TokenEndPointAuthMethod:         authentication.None,
			RevocationEndPointAuthMethod:    authentication.None,
			Scope:                           AUTHING_SCOPE,
		}
		_acp := &sync.Pool{
			New: func() interface{} {
				_ac, err := authentication.NewAuthenticationClient(&_options)
				if err != nil {
					_logger.Error("new authentication client failed", zap.Error(err))
				}
				return _ac
			},
		}

		_client = &Client{
			acp:     _acp,
			logger:  _logger,
			options: &_options,
			cfg:     c,
		}
	}
	return _client
}

// GenerateOIDCLoginURL 生成 OIDC 协议的用户登录链接
func (c *Client) GenerateOIDCLoginURL(redirectUrl string) (*authentication.AuthUrlResult, error) {
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return nil, NewError(fiber.StatusInternalServerError, "GenerateOIDCLoginURL failed", 0, "")
	}
	defer c.acp.Put(ac)

	if len(redirectUrl) == 0 {
		redirectUrl = fmt.Sprintf("%s/auth/login", c.cfg.Base.CloudWebUIURL)
	}
	req := &authentication.OIDCAuthURLParams{
		RedirectUri: redirectUrl,
		Scope:       AUTHING_SCOPE,
		Forced:      true,
	}
	res, err := ac.BuildAuthorizeUrlByOidc(req)
	if err != nil {
		return nil, err
	}
	c.logger.Debug("GenerateOIDCLoginURL", zap.Any("AuthUrlResult", res))

	return &res, nil
}

// GenerateOIDCLogoutURL 生成 OIDC 协议的用户登出链接
func (c *Client) GenerateOIDCLogoutURL(req *authentication.BuildLogoutURLParams) (string, error) {
	caller := "GenerateOIDCLogoutURL"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return "", NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	var res string
	res, err := ac.BuildLogoutUrl(req)
	if err != nil {
		return "", err
	}
	c.logger.Debug(caller, zap.String("logoutUrl", res))

	return res, nil
}

func (c *Client) Logout(token string) error {
	caller := "LogoutHandler"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	_, err := ac.RevokeToken(token)
	if err != nil {
		return err
	}

	return nil
}
