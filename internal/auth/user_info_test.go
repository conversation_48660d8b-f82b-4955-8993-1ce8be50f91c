// Package auth -----------------------------
// @file      : user_info_test.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/19 10:45
// -------------------------------------------
package auth

import (
	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"go.uber.org/zap"
	"sync"
	"testing"
)

var (
	_accessToken  = "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	_refreshToken = "FGLaK35dPiiYaMfHHN9PaSiYZAnnqxnFSLG2oQKtuDn"
	_idToken      = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RKVeEyR_aK42A357sip7TUQt1urzRkZAtak41cb0Qhw"
)

func TestMain(t *testing.M) {
	_logger := zap.L().With(zap.String("module", "authing"))
	options := authentication.AuthenticationClientOptions{
		AppId:                           AUTHING_APP_ID,                  // Authing 应用 ID
		AppSecret:                       AUTHING_APP_SECRET,              // Authing 应用密钥
		AppHost:                         AUTHING_APP_HOST,                // Authing 应用域名，如https://example.authing.cn。注意：Host 地址为示例样式，不同版本用户池的应用 Host 地址形式有所差异，实际地址以 自建应用->应用配置->认证配置 下 `认证地址 `字段为准。
		RedirectUri:                     AUTHING_APP_REDIRECT_URI,        // Authing 应用配置的登录回调地址
		LogoutRedirectUri:               AUTHING_APP_REDIRECT_LOGOUT_URI, // Authing 应用配置的登出回调地址
		Protocol:                        AUTHING_PROTOCOL,                // 应用协议类型，默认为 oidc。可选值为 oidc、oauth、cas、saml
		IntrospectionEndPointAuthMethod: authentication.None,
		TokenEndPointAuthMethod:         authentication.None,
		RevocationEndPointAuthMethod:    authentication.None,
		Scope:                           AUTHING_SCOPE,
	}
	_acp := &sync.Pool{
		New: func() interface{} {
			_ac, err := authentication.NewAuthenticationClient(&options)
			if err != nil {
				_logger.Error("new authentication client failed", zap.Error(err))
			}
			return _ac
		},
	}
	_client = &Client{
		acp:     _acp,
		logger:  _logger,
		options: &options,
	}
	t.Run()
}

func TestClient_GetUserInfoByAccessToken(t *testing.T) {
	type args struct {
		accessToken string
	}
	tests := []struct {
		name string
		args args
	}{
		// Add test cases.
		{name: "t1", args: args{accessToken: _accessToken}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := _client.GetUserInfoByAccessToken(tt.args.accessToken)
			if err != nil {
				t.Errorf("GetUserInfoByAccessToken() error = %v", err)
				return
			}
			t.Logf("GetUserInfoByAccessToken() = %v", *got)
		})
	}
}

func TestClient_GetNewAccessTokenByRefreshToken(t *testing.T) {
	type args struct {
		refreshToken string
	}
	tests := []struct {
		name string
		args args
	}{
		// Add test cases.
		{name: "t1", args: args{refreshToken: _refreshToken}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := _client.GetNewAccessTokenByRefreshToken(tt.args.refreshToken)
			if err != nil {
				t.Errorf("GetNewAccessTokenByRefreshToken() error = %v", err)
				return
			}
			t.Logf("GetNewAccessTokenByRefreshToken() = %v", *got)
		})
	}
}

func TestClient_IntrospectAccessTokenOffline(t *testing.T) {
	type args struct {
		accessToken string
	}
	tests := []struct {
		name string
		args args
	}{
		// Add test cases.
		{name: "t1", args: args{accessToken: _accessToken}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := _client.IntrospectAccessTokenOffline(tt.args.accessToken)
			if err != nil {
				t.Errorf("IntrospectAccessTokenOffline() error = %v", err)
				return
			}
			t.Logf("IntrospectAccessTokenOffline() = %v", *got)
		})
	}
}
