// Package auth -----------------------------
// @file      : user_info.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/16 09:46
// -------------------------------------------
package auth

import (
	"encoding/json"
	"fmt"
	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"github.com/Authing/authing-golang-sdk/v3/constant"
	"github.com/Authing/authing-golang-sdk/v3/dto"
	"github.com/gofiber/fiber/v3"
	"github.com/valyala/fasthttp"
	"go.uber.org/zap"
	"time"
)

type Address struct {
	Country    string  `json:"country"`
	PostalCode string  `json:"postal_code"`
	Region     *string `json:"region"`
	Formatted  *string `json:"formatted"`
}

type ExtendedFields struct {
	AgreeTerms    bool   `json:"agreeTerms"`
	DefaultDevice string `json:"defaultDevice"`
	UiTheme       string `json:"uiTheme"`
	UxImprovePlan string `json:"uxImprovePlan"`
}

type UserInfo struct {
	Name                string         `json:"name"`
	GivenName           string         `json:"given_name"`
	MiddleName          string         `json:"middle_name"`
	FamilyName          string         `json:"family_name"`
	Nickname            string         `json:"nickname"`
	PreferredUsername   string         `json:"preferred_username"`
	Profile             string         `json:"profile"`
	Picture             string         `json:"picture"`
	Website             string         `json:"website"`
	Birthdate           time.Time      `json:"birthdate"`
	Gender              string         `json:"gender"`
	Zoneinfo            string         `json:"zoneinfo"`
	Locale              string         `json:"locale"`
	UpdatedAt           time.Time      `json:"updated_at"`
	Email               string         `json:"email"`
	EmailVerified       bool           `json:"email_verified"`
	PhoneNumber         string         `json:"phone_number"`
	PhoneNumberVerified bool           `json:"phone_number_verified"`
	Address             Address        `json:"address"`
	ExternalID          string         `json:"external_id"`
	Username            string         `json:"username"`
	Roles               []string       `json:"roles"`
	ExtendedFields      ExtendedFields `json:"extended_fields"`
	Sub                 string         `json:"sub"`
}

// GetUserInfo 获取用户资料
func (c *Client) GetUserInfo(accessToken string) (*dto.UserDto, error) {
	caller := "GetUserInfo"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return nil, NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	ac.SetAccessToken(accessToken)

	req := &dto.GetProfileDto{
		WithCustomData: true,
	}
	res := ac.GetProfile(req)
	if res == nil {
		return nil, NewError(fiber.StatusBadRequest, fmt.Sprintf("%s failed", caller), 0, "")
	}
	if res.StatusCode != 200 {
		return nil, NewError(res.StatusCode, res.Message, res.ApiCode, res.RequestId)
	}
	c.logger.Debug(caller, zap.Any("UserSingleRespDto", res))

	return &res.Data, nil
}

// GetAccessTokenByCode 通过code获取access_token
func (c *Client) GetAccessTokenByCode(code string) (*authentication.OIDCTokenResponse, error) {
	caller := "GetAccessTokenByCode"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return nil, NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	res, err := ac.GetAccessTokenByCode(code)
	if err != nil {
		return nil, err
	}
	c.logger.Debug(caller, zap.Any("OIDCTokenResponse", res))

	return &res, nil
}

const (
	LightUiTheme string = "light" // 浅色模式

	OnDesktopAssistant  string = "on"
	OffDesktopAssistant string = "off"

	OnNoConfirmMode  string = "on"
	OffNoConfirmMode string = "off"

	OnUxImprovePlan  string = "on"
	OffUxImprovePlan string = "off"
)

// GetUserInfoByAccessToken 从Token读取用户信息
func (c *Client) GetUserInfoByAccessToken(accessToken string) (*UserInfo, error) {
	caller := "GetUserInfoByAccessToken"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return nil, NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	res, err := ac.SendProtocolHttpRequest(&authentication.ProtocolRequestOption{
		Method: fasthttp.MethodPost,
		Url:    c.options.AppHost + "/oidc/me",
		Headers: map[string]string{
			"x-authing-request-from": constant.SdkName,
			"x-authing-sdk-version":  constant.SdkVersion,
			"Authorization":          `Bearer ` + accessToken,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("根据 access token 获取用户信息时失败: %w", err)
	}
	if res.StatusCode != 200 {
		return nil, fmt.Errorf("根据 access token 获取用户信息失败[%d]:%s", res.StatusCode, res.Body)
	}

	var userInfo UserInfo
	err = json.Unmarshal(res.Body, &userInfo)
	if err != nil {
		return nil, fmt.Errorf("无法解析用户信息:%w", err)
	}

	if userInfo.ExtendedFields.UiTheme == "" {
		userInfo.ExtendedFields.UiTheme = LightUiTheme
	}

	if userInfo.ExtendedFields.UxImprovePlan == "" {
		userInfo.ExtendedFields.UxImprovePlan = OnUxImprovePlan
	}

	c.logger.Debug(caller, zap.Any("UserInfo", userInfo))

	return &userInfo, nil
}

// GetNewAccessTokenByRefreshToken 根据 refresh token 获取新的 access token
func (c *Client) GetNewAccessTokenByRefreshToken(refreshToken string) (*authentication.OIDCTokenResponse, error) {
	caller := "GetNewAccessTokenByRefreshToken"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return nil, NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	res, err := ac.GetNewAccessTokenByRefreshToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("根据 refresh token 获取新的 access token 失败: %w", err)
	}

	var tokenResponse authentication.OIDCTokenResponse
	err = json.Unmarshal([]byte(res), &tokenResponse)
	if err != nil {
		return nil, fmt.Errorf("无法解析 access token: %w", err)
	}

	c.logger.Debug(caller, zap.Any("OIDCTokenResponse", tokenResponse))

	return &tokenResponse, nil
}

// IntrospectAccessTokenOffline 离线校验 access Token
func (c *Client) IntrospectAccessTokenOffline(accessToken string) (*authentication.AccessTokenClaims, error) {
	caller := "IntrospectAccessTokenOffline"
	ac, ok := c.acp.Get().(*authentication.AuthenticationClient)
	if !ok {
		return nil, NewError(fiber.StatusInternalServerError, fmt.Sprintf("%s failed", caller), 0, "")
	}
	defer c.acp.Put(ac)

	res, err := ac.IntrospectAccessTokenOffline(accessToken)
	if err != nil {
		return nil, fmt.Errorf("无法解析 access token: %w", err)
	}

	c.logger.Debug(caller, zap.Any("AccessTokenClaims", res))

	return res, nil
}
