package main

import (
	"flag"
	"log"
	"net"
	"strconv"
	"strings"
	"time"

	pb "AgenticAI-Client/pkg/workflow/gen"
	"google.golang.org/grpc"
)

type server struct {
	pb.ChatServiceServer
}

func (s *server) Chat(stream grpc.BidiStreamingServer[pb.ChatRequest, pb.ChatReply]) error {
	req, err := stream.Recv()
	if err != nil {
		return err
	}
	fullResponse := "这是一个模拟的AI响应。基于您的输入'" + req.Prompt +
		"'，我将逐步返回结果。这是流式传输的演示。"

	words := strings.Split(fullResponse, " ")

	for _, word := range words {
		err = stream.Send(&pb.ChatReply{
			ConversationId: req.ConversationId,
			Response: &pb.ChatReply_TextResponse{
				TextResponse: &pb.TextResponse{
					Content: word,
				},
			},
			IsEnd: false,
		})
		if err != nil {
			return err
		}
		time.Sleep(1 * time.Second) // 模拟处理延迟
	}

	// 发送结束标记
	return stream.Send(&pb.ChatReply{
		ConversationId: req.ConversationId,
		Response: &pb.ChatReply_TextResponse{
			TextResponse: &pb.TextResponse{
				Content: "End",
			},
		},
		IsEnd: true,
	})
}

func main() {
	// 定义一个命令行参数，用于指定端口号
	var port int
	flag.IntVar(&port, "port", 50051, "指定端口号，默认为50051")

	// 解析命令行参数
	flag.Parse()

	lis, err := net.Listen("tcp", ":"+strconv.Itoa(port))
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	s := grpc.NewServer()
	pb.RegisterChatServiceServer(s, &server{})

	log.Printf("server listening at %v", lis.Addr())
	if err := s.Serve(lis); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
