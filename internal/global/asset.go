// Package global -----------------------------
// @file      : asset.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/30 10:16
// -------------------------------------------
package global

import (
	"AgenticAI-Client/internal/constant"
	"embed"

	"github.com/wailsapp/wails/v3/pkg/icons"
)

var assetsMap = map[string][]byte{}

func LoadAssets(assets embed.FS) error {
	assetPic := []string{
		constant.LogoPath,
		constant.EndPic, constant.EndHoverPic,
		constant.RunningPic, constant.RunningHoverPic,
		constant.ResumePic, constant.ResumeHoverPic,
		constant.PendingPic, constant.PendingHoverPic,
		constant.SettingPic, constant.SettingHoverPic,
		constant.LogoutPic, constant.LogoutHoverPic,
		constant.ErrorPic, constant.ErrorHoverPic,
		constant.StopPic, constant.StopHoverPic,
		constant.HomePic, constant.HomeHoverPic,
		constant.NewTaskPic, constant.NewTaskHoverPic,
		constant.AccountPic, constant.ExitPic,
		constant.FloatWindowHidePic, constant.FloatWindowShowPic,
	}

	for _, v := range assetPic {
		data, err := assets.ReadFile(v)
		if err != nil {
			return err
		}
		assetsMap[v] = data
	}

	return nil
}

func ReadAssets(key string) []byte {
	v, ok := assetsMap[key]
	if !ok {
		return icons.ApplicationLightMode256
	}
	return v
}
