// Package config -----------------------------
// @file      : emqx.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/15 16:00
// -------------------------------------------
package config

import (
	"errors"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"
	"time"
)

var loggerModule = zap.String("module", "emqx")

var messagePubHandler mqtt.MessageHandler = func(client mqtt.Client, msg mqtt.Message) {
	zap.L().With(loggerModule).Info("Received message from topic", zap.String("payload", string(msg.Payload())), zap.String("topic", msg.Topic()))
}

var connectHandler mqtt.OnConnectHandler = func(client mqtt.Client) {
	zap.L().With(loggerModule).Info("Connected to emqx broker successfully")
}

var connectLostHandler mqtt.ConnectionLostHandler = func(client mqtt.Client, err error) {
	zap.L().With(loggerModule).Warn("broker connect lost", zap.Error(err))
	// 尝试重新连接
	for !client.IsConnected() {
		zap.L().With(loggerModule).Debug("Reconnecting...")
		token := client.Connect()
		if token.Wait() && token.Error() != nil {
			zap.L().With(loggerModule).Debug("Reconnect failed. Retrying in 5 seconds...", zap.Error(token.Error()))
			time.Sleep(5 * time.Second)
		} else {
			zap.L().With(loggerModule).Debug("Reconnected successfully.")
			break
		}
	}
}

// EMQXConfig emqx 配置
// 参考emqx官方文档：https://docs.emqx.com/zh/emqx/latest/
// 以及示例文档：https://www.emqx.com/en/blog/how-to-use-mqtt-in-golang
type EMQXConfig struct {
	// ID client id
	ID string `yaml:"id"`
	// Addr emqx server addr, eg: tcp://127.0.0.1:1883
	Addr string `yaml:"addr"`
	// Username
	Username string `yaml:"username"`
	// Password
	Password string `yaml:"password"`
	// MessageHandler is a callback type which can be set to be executed upon the arrival of messages published to topics to which the client is subscribed.
	MessageHandler mqtt.MessageHandler `yaml:"-"`
	// ConnectHandler OnConnectHandler is a callback that is called when the client state changes from unconnected/disconnected to connected.
	// Both at initial connection and on reconnection.
	OnConnectHandler mqtt.OnConnectHandler `yaml:"-"`
	// ConnectionLostHandler is a callback that is called when the client loses its connection to the broker.
	ConnectionLostHandler mqtt.ConnectionLostHandler `yaml:"-"`
	TLSConfig             *TLSConfig                 `yaml:"TLSConfig"`
}

func (cfg *EMQXConfig) Complete() []error {
	var ErrList []error
	if cfg == nil {
		ErrList = append(ErrList, errors.New("emqx broker config is nil"))
		return ErrList
	}

	cfg.MessageHandler = messagePubHandler
	cfg.OnConnectHandler = connectHandler
	cfg.ConnectionLostHandler = connectLostHandler

	if cfg.TLSConfig != nil && cfg.TLSConfig.Enable {
		cfg.TLSConfig.TLS = cfg.TLSConfig.NewTlsConfig()
	}

	return ErrList
}
