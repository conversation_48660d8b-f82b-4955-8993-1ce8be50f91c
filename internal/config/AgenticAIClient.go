package config

import (
	"AgenticAI-Client/internal/caller"
	"AgenticAI-Client/internal/constant"
	logger "AgenticAI-Client/pkg/log"
	"AgenticAI-Client/pkg/oss"
	"AgenticAI-Client/pkg/utils"
	"log"
	"os"
	"path"
	"path/filepath"
	"time"

	"github.com/spf13/viper"
	utilErrors "k8s.io/apimachinery/pkg/util/errors"
)

type TuriXConfig struct {
	Base          Base                       `yaml:"base" mapstructure:"base"`
	Path          PathConfig                 `yaml:"dir" mapstructure:"dir"`
	Server        ServerConfig               `yaml:"server" mapstructure:"server"`
	Log           logger.Config              `yaml:"log" mapstructure:"log"`
	AIAgentServer caller.AIAgentServerConfig `yaml:"AIAgent" mapstructure:"AIAgent"`

	OSS     oss.Config    `yaml:"oss" mapstructure:"oss"`
	Scanner ScannerConfig `yaml:"scanner" mapstructure:"scanner"`
}

var _config *TuriXConfig

func GetTuriXConfig() *TuriXConfig {
	if _config != nil {
		return _config
	}
	c, err := newTuriXConfig()
	if err != nil {
		log.Fatalf("failed to create config: %v", err)
	}
	err = c.Complete()
	if err != nil {
		log.Fatalf("failed to fill config: %v", err)
	}
	err = c.Convert()
	if err != nil {
		log.Fatalf("failed to convert config: %v", err)
	}
	_config = c

	return _config
}

func newTuriXConfig() (*TuriXConfig, error) {
	// Find app/work directory
	app, _ := os.Executable()
	appDir := path.Dir(app)
	wd, err := os.Getwd()
	if err != nil {
		return nil, err
	}

	// Search config in work directory with name "config" (without extension).
	viper.AddConfigPath(appDir)
	viper.AddConfigPath(wd)
	viper.SetConfigType("yaml")
	viper.SetConfigName("config")
	viper.AutomaticEnv()
	err = viper.ReadInConfig()
	if err != nil {
		return nil, err
	}
	log.Println("Using config file:", viper.ConfigFileUsed())

	var config TuriXConfig
	err = viper.Unmarshal(&config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func (conf *TuriXConfig) Complete() error {
	// base
	if conf.Base.CloudWebUIURL == "" {
		conf.Base.CloudWebUIURL = constant.CloudWebUIURL
	}
	if conf.Base.CloudWebUIURLDev == "" {
		conf.Base.CloudWebUIURLDev = constant.CloudWebUIURLDev
	}
	if conf.Base.AuthingCallbackURL == "" {
		conf.Base.AuthingCallbackURL = constant.CloudWebUIURL
	}

	// dir
	if conf.Path.WorkHome == "" {
		app, err := os.Executable()
		if err != nil {
			return err
		}
		appDir := path.Dir(app)
		conf.Path.WorkHome = appDir
	}
	if conf.Path.LogDir == "" {
		conf.Path.LogDir = constant.DefaultLogDir
	}
	if conf.Path.DataDir == "" {
		conf.Path.DataDir = constant.DefaultDataDir
	}
	dir, err := utils.GetAppSupportDir(constant.APPName)
	if err == nil {
		conf.Path.DataDir = dir
	}
	if conf.Path.UpdateAPPDir == "" {
		conf.Path.UpdateAPPDir = path.Join(dir, "update", constant.APPName)
	}
	if conf.Path.UpdateAgentDir == "" {
		conf.Path.UpdateAgentDir = path.Join(dir, "update", constant.AIAgentName)
	}
	if conf.Path.UpdateStatusInfoLocation == "" {
		conf.Path.UpdateStatusInfoLocation = path.Join(dir, "update", "pending.json")
	}

	// log
	if conf.Log.MaxSizeMB == 0 {
		conf.Log.MaxSizeMB = constant.LogMaxSizeMegaBytes
	}
	if conf.Log.Backup == 0 {
		conf.Log.Backup = constant.LogBackupCount
	}
	if conf.Log.MaxAgeDay == 0 {
		conf.Log.MaxAgeDay = constant.LogMaxAgeDay
	}
	if conf.Log.Path == "" {
		conf.Log.Path = filepath.Join(conf.Path.LogDir, constant.APPName+".log")
	}

	// server
	if conf.Server.Addr == "" {
		conf.Server.Addr = constant.DefaultServerAddr
	}
	if conf.Server.Port == 0 {
		conf.Server.Port = constant.DefaultServerPort
	}
	if conf.Server.Limit < 0 {
		conf.Server.Limit = 10
	}
	if conf.Server.Burst < conf.Server.Limit {
		conf.Server.Burst = conf.Server.Limit
	}
	if conf.Server.RequestTimeout == 0 {
		conf.Server.RequestTimeout = time.Duration(5) * time.Second
	}

	// ai agent
	if conf.AIAgentServer.Path == "" {
		execPath, _ := os.Executable()
		dir := filepath.Dir(execPath)
		// serverPath := filepath.Join(dir, constant.AIAgentName+".app")
		serverPath := filepath.Join(dir, constant.AIAgentName)
		conf.AIAgentServer.Path = serverPath
	}
	if conf.AIAgentServer.Host == "" {
		conf.AIAgentServer.Host = constant.DefaultAIAgentRPCHost
	}
	if conf.AIAgentServer.Port <= 0 {
		conf.AIAgentServer.Port = constant.DefaultAIAgentRPCPort
	}

	// EMQX配置已移除，不再需要MQTT broker

	// oss
	if conf.OSS.Endpoint == "" {
		conf.OSS.Endpoint = constant.DefaultOSSEndpoint
	}
	if conf.OSS.AccessKeyID == "" {
		conf.OSS.AccessKeyID = constant.DefaultOSSAccessKeyID
	}
	if conf.OSS.AccessKeySecret == "" {
		conf.OSS.AccessKeySecret = constant.DefaultOSSAccessKeySecret
	}
	if conf.OSS.BucketName == "" {
		conf.OSS.BucketName = constant.DefaultOSSBucketName
	}
	if conf.OSS.URLPrefix == "" {
		conf.OSS.URLPrefix = constant.DefaultOSSUrlPrefix
	}
	if conf.OSS.Timeout <= 0 {
		conf.OSS.Timeout = constant.DefaultOSSTimeout
	}
	if conf.OSS.ExpireTime <= 0 {
		conf.OSS.ExpireTime = constant.DefaultOSSExpireTime
	}

	// scanner
	if conf.Scanner.Heartbeat.ScanLoopInterval == "" {
		conf.Scanner.Heartbeat.ScanLoopInterval = constant.ScanHeartbeatDefaultInterval
	}
	if conf.Scanner.PermissionCheck.ScanLoopInterval == "" {
		conf.Scanner.PermissionCheck.ScanLoopInterval = constant.ScanPermissionCheckDefaultInterval
	}
	if conf.Scanner.InnerUI.ScanLoopInterval == "" {
		conf.Scanner.InnerUI.ScanLoopInterval = constant.ScanGetMousePosDefaultInterval
	}
	if conf.Scanner.InnerUI.Disabled == false {
		// 默认关闭全局鼠标位置监听
		conf.Scanner.InnerUI.Disabled = true
	}

	return nil
}

func (conf *TuriXConfig) Convert() error {
	var ErrList []error

	// log
	conf.Log.Complete()
	logger.Init(conf.Base.Console, &conf.Log)

	// server
	ErrList = append(ErrList, conf.Server.Complete()...)

	// path
	ErrList = append(ErrList, conf.Path.Complete()...)

	// ai agent
	ErrList = append(ErrList, conf.AIAgentServer.Complete()...)

	// EMQX配置已移除

	// scanner
	ErrList = append(ErrList, conf.Scanner.Complete()...)

	// oss
	conf.OSS.Complete()

	return utilErrors.NewAggregate(ErrList)
}
