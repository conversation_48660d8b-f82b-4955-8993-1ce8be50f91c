// Package config -----------------------------
// @file      : base.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/11 15:26
// -------------------------------------------
package config

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/pkg/utils"
	"errors"
	"fmt"
	"time"
)

type Base struct {
	Console            bool   `yaml:"console" mapstructure:"console"`
	Verbose            bool   `yaml:"verbose" mapstructure:"verbose"`
	PProfPort          int    `yaml:"pprofPort" mapstructure:"pprofPort"`
	CloudWebUIURL      string `yaml:"cloudWebUIURL" mapstructure:"cloudWebUIURL"`
	CloudWebUIURLDev   string `yaml:"cloudWebUIURLDev" mapstructure:"cloudWebUIURLDev"`
	AuthingCallbackURL string `yaml:"authingCallbackURL" mapstructure:"authingCallbackURL"`
}

type PathConfig struct {
	WorkHome                 string `yaml:"workHome" mapstructure:"workHome"`
	LogDir                   string `yaml:"logDir" mapstructure:"logDir"`
	DataDir                  string `yaml:"dataDir" mapstructure:"dataDir"`
	UpdateAPPDir             string `yaml:"updateAppDir" mapstructure:"updateAppDir"`
	UpdateAgentDir           string `yaml:"updateAgentDir" mapstructure:"updateAgentDir"`
	UpdateStatusInfoLocation string
}

func (cfg *PathConfig) Complete() []error {
	var ErrList []error
	if cfg == nil {
		ErrList = append(ErrList, errors.New("path config is nil"))
		return ErrList
	}

	if cfg.WorkHome != "" {
		err := utils.MakeDir(cfg.WorkHome)
		if err != nil {
			ErrList = append(ErrList, err)
		}
	}
	if cfg.LogDir != "" {
		err := utils.MakeDir(cfg.LogDir)
		if err != nil {
			ErrList = append(ErrList, err)
		}
	}

	if cfg.DataDir != "" {
		err := utils.MakeDir(cfg.DataDir)
		if err != nil {
			ErrList = append(ErrList, err)
		}
	}

	if cfg.UpdateAPPDir != "" {
		err := utils.MakeDir(cfg.UpdateAPPDir)
		if err != nil {
			ErrList = append(ErrList, err)
		}
	}

	if cfg.UpdateAgentDir != "" {
		err := utils.MakeDir(cfg.UpdateAgentDir)
		if err != nil {
			ErrList = append(ErrList, err)
		}
	}

	return ErrList
}

type ServerConfig struct {
	Addr              string        `yaml:"addr"`
	Port              int           `yaml:"port"`
	Limit             int           `yaml:"limit"`
	Burst             int           `yaml:"burst"`
	RequestTimeout    time.Duration `yaml:"requestTimeout"`
	RedirectUrl       string        `yaml:"redirectUrl"`
	RedirectLogoutUrl string        `yaml:"redirectLogoutUrl"`
}

func (cfg *ServerConfig) Complete() []error {
	var ErrList []error

	port, err := utils.FindFreePort(cfg.Port)
	if err != nil {
		ErrList = append(ErrList, err)
	}
	cfg.Port = port

	if cfg.RedirectUrl == "" {
		cfg.RedirectUrl = fmt.Sprintf(constant.DefaultServerRedirectUrl, cfg.Port)
	}
	if cfg.RedirectLogoutUrl == "" {
		cfg.RedirectLogoutUrl = fmt.Sprintf(constant.DefaultServerRedirectLogoutUrl, cfg.Port)
	}

	return ErrList
}
