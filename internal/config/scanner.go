// Package config -----------------------------
// @file      : scanner.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 17:03
// -------------------------------------------
package config

import (
	"AgenticAI-Client/internal/constant"
	"errors"
	"go.uber.org/zap"
	"time"
)

type ScannerConfig struct {
	Heartbeat       HeartbeatConfig       `yaml:"heartbeat"`
	PermissionCheck PermissionCheckConfig `yaml:"turiCoreConfig"`
	InnerUI         InnerUIReporterConfig `yaml:"innerUI"`
}

func (cfg *ScannerConfig) Complete() []error {
	var ErrList []error
	if cfg == nil {
		ErrList = append(ErrList, errors.New("scanner config is nil"))
		return ErrList
	}

	scanDuration, err := time.ParseDuration(cfg.Heartbeat.ScanLoopInterval)
	if err != nil || scanDuration.Seconds() < constant.ScanLoopFloorLimit {
		zap.L().Warn("invalid ScanLoopInterval param, use default value", zap.String("scan_loop_interval", cfg.Heartbeat.ScanLoopInterval), zap.Int("ScanLoopFloorLimit(s)", constant.ScanLoopFloorLimit))
		scanDuration = constant.ScanLoopFloorLimit * time.Second
	}
	cfg.Heartbeat.ScanLoopIntervalDuration = scanDuration

	scanDuration, err = time.ParseDuration(cfg.PermissionCheck.ScanLoopInterval)
	if err != nil {
		zap.L().Warn("invalid ScanLoopInterval param, use default value", zap.String("scan_loop_interval", cfg.PermissionCheck.ScanLoopInterval), zap.Int("ScanLoopFloorLimit(s)", constant.ScanLoopFloorLimit))
		scanDuration = constant.ScanLoopFloorLimit * time.Second
	}
	cfg.PermissionCheck.ScanLoopIntervalDuration = scanDuration

	scanDuration, err = time.ParseDuration(cfg.InnerUI.ScanLoopInterval)
	if err != nil {
		zap.L().Warn("invalid ScanLoopInterval param, use default value", zap.String("scan_loop_interval", cfg.InnerUI.ScanLoopInterval), zap.Int("ScanLoopFloorLimit(s)", constant.ScanLoopFloorLimit))
		scanDuration = constant.ScanLoopFloorLimit * time.Second
	}
	cfg.InnerUI.ScanLoopIntervalDuration = scanDuration

	return ErrList
}

type HeartbeatConfig struct {
	ScanLoopInterval         string `yaml:"scan_loop_interval,omitempty"`
	ScanLoopIntervalDuration time.Duration
	Disabled                 bool `yaml:"disabled"`
}

type PermissionCheckConfig struct {
	ScanLoopInterval         string `yaml:"scan_loop_interval,omitempty"`
	ScanLoopIntervalDuration time.Duration
	Disabled                 bool `yaml:"disabled"`
}

type InnerUIReporterConfig struct {
	ScanLoopInterval         string `yaml:"scan_loop_interval,omitempty"`
	ScanLoopIntervalDuration time.Duration
	Disabled                 bool `yaml:"disabled"`
}
