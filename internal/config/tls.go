// Package config -----------------------------
// @file      : tls.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 15:38
// -------------------------------------------
package config

import (
	"crypto/tls"
	"crypto/x509"
	"go.uber.org/zap"
	"os"
)

type TLSConfig struct {
	Enable   bool        `yaml:"enable"`
	CA       string      `yaml:"ca"`
	CertFile string      `yaml:"certFile"`
	KeyFile  string      `yaml:"keyFile"`
	TLS      *tls.Config `yaml:"-"`
}

func (c *TLSConfig) NewTlsConfig() *tls.Config {
	if c == nil {
		return &tls.Config{}
	}

	ca, err := os.ReadFile(c.CA)
	if err != nil {
		zap.L().Fatal("Failed to read CA certificate", zap.Error(err))
	}
	pool := x509.NewCertPool()
	pool.AppendCertsFromPEM(ca)

	// Import client certificate/key pair
	clientKeyPair, err := tls.LoadX509KeyPair(c.CertFile, c.KeyFile)
	if err != nil {
		zap.L().Fatal("Failed to LoadX509KeyPair", zap.Error(err))
	}
	return &tls.Config{
		RootCAs:            pool,
		ClientAuth:         tls.NoClientCert,
		ClientCAs:          nil,
		InsecureSkipVerify: true,
		Certificates:       []tls.Certificate{clientKeyPair},
	}
}

func (c *TLSConfig) NewTlsConfigWithoutClientCert() *tls.Config {
	if c == nil {
		return &tls.Config{}
	}

	ca, err := os.ReadFile(c.CA)
	if err != nil {
		zap.L().Fatal("Failed to read CA certificate", zap.Error(err))
	}
	pool := x509.NewCertPool()
	pool.AppendCertsFromPEM(ca)

	return &tls.Config{
		RootCAs: pool,
	}
}
