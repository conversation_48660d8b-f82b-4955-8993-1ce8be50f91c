version: '3'

includes:
  common: ./build/Taskfile.yml
  windows: ./build/windows/Taskfile.yml
  darwin: ./build/darwin/Taskfile.yml
  linux: ./build/linux/Taskfile.yml

vars:
  APP_NAME: "TuriX"
  BIN_DIR: "bin"
  VITE_PORT: '{{.WAILS_VITE_PORT | default 9245}}'
  VERSION:
    sh: |
      awk -F '=' '/^VERSION/ {gsub(/^[ \t]+|[ \t]+$/, "", $2); print $2}' Makefile
  PRODUCTION:
    sh: |
      version=$(awk -F '=' '/^VERSION/ {gsub(/^[ \t]+|[ \t]+$|["'\'']/, "", $2); print $2}' Makefile)
      # 使用grep检查是否匹配
      if echo "$version" | grep -qE '^[0-9]+\.[0-9]+\.[0-9]+$'; then
        echo true
      else
        echo false
      fi

tasks:
  build:
    summary: Builds the application
    cmds:
      - task: "{{OS}}:build"

  package:
    summary: Packages a production build of the application
    cmds:
      - task: "{{OS}}:package"

  run:
    summary: Runs the application
    cmds:
      - task: "{{OS}}:run"

  dev:
    summary: Runs the application in development mode
    cmds:
      - wails3 dev -config ./build/config.yml -port {{.VITE_PORT}}
