# Wails前后端通信完整指南

## 概述

在移除MQTT后，项目现在使用Wails的原生通信机制进行前后端数据交换：

1. **前端 → 后端**：通过直接调用Go方法
2. **后端 → 前端**：通过Wails Event系统

## 1. 前端调用后端方法

### 后端Go方法定义

在 `internal/app/app.go` 中，TuriXApp结构体提供了以下公开方法：

```go
// SendChatMessage 前端发送聊天消息到后端
func (a *TuriXApp) SendChatMessage(message string) error

// GetDeviceInfo 获取设备信息
func (a *TuriXApp) GetDeviceInfo() map[string]interface{}

// UpdateSettings 更新应用设置
func (a *TuriXApp) UpdateSettings(settings map[string]interface{}) error

// TriggerTask 触发任务执行
func (a *TuriXApp) TriggerTask(taskData map[string]interface{}) error
```

### 前端TypeScript调用

```typescript
import { TuriXApp } from '@/bindings/AgenticAI-Client/internal/app'

// 发送聊天消息
await TuriXApp.SendChatMessage("Hello from frontend!")

// 获取设备信息
const deviceInfo = await TuriXApp.GetDeviceInfo()
console.log(deviceInfo) // { deviceID: "xxx", isLogin: true, userID: "xxx" }

// 更新设置
await TuriXApp.UpdateSettings({
  theme: "dark",
  language: "zh-CN"
})

// 触发任务
await TuriXApp.TriggerTask({
  type: "automation",
  command: "open browser",
  params: { url: "https://example.com" }
})
```

## 2. 后端发送事件到前端

### 后端发送事件

```go
// 在任何Go代码中发送事件到前端
a.eventBus.EmitChatResponse(response)
a.eventBus.EmitDeviceStatus(status)
a.eventBus.EmitDeviceHeartbeat(heartbeat)
```

### 前端监听事件

```typescript
const client = useClient()

// 监听聊天响应
client.on('event.chat.response', (response: any) => {
  console.log('收到聊天响应:', response)
  // 处理响应，更新UI等
})

// 监听设备状态
client.on('event.device.status', (status: any) => {
  console.log('设备状态更新:', status)
  // 处理设备状态变化
})

// 监听设备心跳
client.on('event.device.heartbeat', (heartbeat: any) => {
  console.log('设备心跳:', heartbeat)
  // 处理心跳信息
})
```

## 3. 完整的双向通信示例

### Vue组件示例

```vue
<script setup lang="ts">
import { TuriXApp } from '@/bindings/AgenticAI-Client/internal/app'

const client = useClient()
const messages = ref<any[]>([])
const inputText = ref('')

// 监听后端事件
onMounted(() => {
  client.on('event.chat.response', (response: any) => {
    messages.value.push({
      type: 'response',
      content: response,
      timestamp: Date.now()
    })
  })
})

// 发送消息到后端
const sendMessage = async () => {
  if (!inputText.value.trim()) return
  
  try {
    // 添加到本地消息列表
    messages.value.push({
      type: 'user',
      content: inputText.value,
      timestamp: Date.now()
    })
    
    // 发送到后端处理
    await TuriXApp.SendChatMessage(inputText.value)
    
    inputText.value = ''
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

// 获取设备信息
const getDeviceInfo = async () => {
  try {
    const info = await TuriXApp.GetDeviceInfo()
    console.log('设备信息:', info)
  } catch (error) {
    console.error('获取设备信息失败:', error)
  }
}
</script>

<template>
  <div>
    <!-- 消息列表 -->
    <div v-for="msg in messages" :key="msg.timestamp">
      <div :class="msg.type === 'user' ? 'user-message' : 'bot-message'">
        {{ msg.content }}
      </div>
    </div>
    
    <!-- 输入框 -->
    <input 
      v-model="inputText" 
      @keydown.enter="sendMessage"
      placeholder="输入消息..."
    />
    
    <!-- 按钮 -->
    <button @click="sendMessage">发送</button>
    <button @click="getDeviceInfo">获取设备信息</button>
  </div>
</template>
```

## 4. 数据流向图

```
前端用户操作
    ↓
前端调用Go方法 (TuriXApp.SendChatMessage)
    ↓
后端处理逻辑 (AIClient, UserService等)
    ↓
后端发送事件 (eventBus.EmitChatResponse)
    ↓
前端接收事件 (client.on('event.chat.response'))
    ↓
前端更新UI
```

## 5. 优势

1. **类型安全**：TypeScript绑定提供完整的类型检查
2. **直接调用**：前端可以直接调用后端方法，无需HTTP请求
3. **实时通信**：事件系统提供实时的后端→前端通信
4. **简单高效**：比MQTT更简单，性能更好
5. **原生支持**：Wails原生支持，无需额外依赖

## 6. 注意事项

1. **方法必须是公开的**：Go方法名必须以大写字母开头
2. **参数类型**：支持基本类型、结构体、map、slice等
3. **错误处理**：前端调用会返回Promise，需要适当的错误处理
4. **事件命名**：事件名称需要在constant.go中定义
5. **绑定生成**：修改Go方法后需要运行 `wails3 generate bindings -ts`
