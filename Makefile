# 系统信息
GOHOSTOS := $(shell go env GOHOSTOS)
GOARCH := $(shell go env GOARCH)

# pro
#VERSION ?= "0.0.2"
#CORE_VERSION ?= "0.0.2"
# dev
VERSION ?= "0.0.2-alpha.2.11"
CORE_VERSION ?= "0.0.2-alpha.11"

# 路径定义（绝对路径）
CURDIR_ABS := $(abspath .)
CONFIG := $(CURDIR_ABS)/config.yaml
AGENT := $(CURDIR_ABS)/tools/turix-core
BUILD_DIR := $(CURDIR_ABS)/bin/TuriX.app/Contents/MacOS

export CERT := "Developer ID Application: Lingbao Lingjin Technology Co., Ltd (85AZTR632B)"
export APP := $(CURDIR_ABS)/bin/TuriX.app

# 发布路径
RELEASE_APP_INPUT := $(CURDIR_ABS)/tools/go-selfupdate/public/app$(if $(findstring alpha,$(VERSION)),/test)/$(VERSION)/$(GOHOSTOS)-$(GOARCH)
RELEASE_CORE_INPUT := $(CURDIR_ABS)/tools/go-selfupdate/public/agent$(if $(findstring alpha,$(CORE_VERSION)),/test)/$(CORE_VERSION)/$(GOHOSTOS)-$(GOARCH)

# 跨平台处理
ifeq ($(GOHOSTOS),windows)
    PATH_SEP := \\
    EXE_SUFFIX := .exe
else
    PATH_SEP := /
    EXE_SUFFIX :=
endif


.PHONY: init
# init env
init:
	go install github.com/wailsapp/wails/v3/cmd/wails3
	go install github.com/bufbuild/buf/cmd/buf@v1.52.0

.PHONY: generate
# generate internal proto/ TypeScript bindings
generate:
	cd ./pkg/workflow && buf build && buf generate
	wails3 generate bindings -ts
	# go generate ./...

.PHONY: build
# build and packet the wails v3 app
build:
	rm -rf $(CURDIR_ABS)/bin/*
	wails3 package darwin
	cp $(CONFIG) $(BUILD_DIR)
	cp -r $(AGENT) $(BUILD_DIR)
	codesign --force --options runtime --timestamp --deep -v --sign $(CERT) $(APP)
	ditto -c -k --keepParent $(APP) $(APP).zip

.PHONY: verify
# verify the wails v3 app
verify:
	codesign --verify --deep --strict --verbose=2 $(APP)
	codesign -dvv $(APP)
	xcrun stapler validate $(APP)
	spctl --assess --type exec --verbose $(APP)

.PHONY: notarization
# notarization the wails v3 app
notarization:
	bash ./script/notarization.sh

.PHONY: release
# release the wails v3 app
release:
	mkdir -p $(RELEASE_APP_INPUT)
	cp -r $(APP).zip $(RELEASE_APP_INPUT)/
	cd $(CURDIR_ABS)/tools/go-selfupdate && go run main.go $(RELEASE_APP_INPUT) $(VERSION)

.PHONY: release-agent
# release the turix-core
release-agent:
	mkdir -p $(RELEASE_CORE_INPUT)/$(GOHOSTOS)-$(GOARCH)
	mkdir -p $(RELEASE_CORE_OUTPUT)
	cp -r $(CURDIR_ABS)/tools/turix-core.zip $(RELEASE_CORE_INPUT)/$(GOHOSTOS)-$(GOARCH)
	cd $(CURDIR_ABS)/tools/go-selfupdate && go run main.go -agent $(RELEASE_CORE_INPUT) $(CORE_VERSION)


.PHONY: all
# generate all
all: init generate build

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help




