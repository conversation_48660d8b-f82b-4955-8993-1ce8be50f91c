// Package utils -----------------------------
// @file      : path.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/11 15:25
// -------------------------------------------
package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
)

func MakeDir(dirPath string) error {
	err := os.MkdirAll(dirPath, 0755)
	if err != nil && !os.IsExist(err) {
		return err
	}

	return nil
}

func GetAppSupportDir(appName string) (string, error) {
	if runtime.GOOS != "darwin" {
		return "", fmt.Errorf("该函数仅适用于 macOS 系统")
	}

	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}

	appSupportDir := filepath.Join(homeDir, "Library", "Application Support", appName)
	return appSupportDir, nil
}

func GetAPPRootPath() (string, error) {
	if runtime.GOOS != "darwin" {
		panic("only support darwin")
	}

	// 获取当前可执行文件的路径
	execPath, err := os.Executable()
	if err != nil {
		return "", fmt.Errorf("无法获取可执行文件路径: %v", err)
	}

	// 解析符号链接，获取真实路径
	execPath, err = filepath.EvalSymlinks(execPath)
	if err != nil {
		return "", fmt.Errorf("无法解析符号链接: %v", err)
	}

	// 获取应用程序包的根目录
	appPath := getAppBundlePath(execPath)
	if appPath == "" {
		return "", fmt.Errorf("未能确定应用程序包的路径")
	}

	return appPath, nil
}

// getAppBundlePath 从可执行文件路径中获取 .app 包的根目录
func getAppBundlePath(execPath string) string {
	// 查找路径中包含 ".app/Contents/MacOS" 的部分
	parts := strings.Split(execPath, string(os.PathSeparator))
	for i := len(parts) - 1; i >= 0; i-- {
		if strings.HasSuffix(parts[i], ".app") {
			return filepath.Join(parts[:i+1]...)
		}
	}
	return ""
}
