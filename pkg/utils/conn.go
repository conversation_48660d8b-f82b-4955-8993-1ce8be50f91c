// Package utils -----------------------------
// @file      : conn.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/30 13:22
// -------------------------------------------
package utils

import (
	pb "AgenticAI-Client/pkg/workflow/gen"
	"fmt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"strconv"
)

var serverConfigStr = fmt.Sprintf(`
{
	"methodConfig": [
		{
			"name":[{"service":"%s"}],
			"retryPolicy":{
				"MaxAttempts": 5,
				"InitialBackoff": "0.1s",
				"MaxBackoff": "1s",
				"BackoffMultiplier": 2,
				"RetryableStatusCodes": [ "UNAVAILABLE" ]
		}
	}]
}
`, pb.ChatService_ServiceDesc.ServiceName)

func NewGrpcClient(host string, port int) (*grpc.ClientConn, error) {
	serverAddr := host + ":" + strconv.Itoa(port)
	conn, err := grpc.NewClient(serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithDefaultServiceConfig(serverConfigStr))
	if err != nil {
		return nil, err
	}

	return conn, nil
}
