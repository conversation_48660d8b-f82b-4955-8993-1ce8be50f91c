// Package utils -----------------------------
// @file      : utils.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/20 11:16
// -------------------------------------------
package utils

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"
)

func MaskPhone(phone string) string {
	if len(phone) >= 11 { // 标准11位手机号
		return phone[:3] + "****" + phone[7:]
	}
	return phone // 非标准长度返回原值
}

// RestartProcessOnPort 关闭监听指定端口的进程并重启子app监听同一端口
//
//	port: 端口号
//	appPath: 子app可执行文件路径
//	args: 启动子app的额外参数（不含--port参数）
//	portArgName: 传递端口参数的名称（如"--port"）
func RestartProcessOnPort(port int, appPath string, args []string, portArgName string) error {
	// 1. 查找监听端口的进程
	out, err := exec.Command("lsof", "-i", fmt.Sprintf(":%d", port), "-sTCP:LISTEN", "-t").Output()
	if err == nil && len(out) > 0 {
		pids := strings.Fields(string(out))
		for _, pid := range pids {
			// 2. kill 进程
			_ = exec.Command("kill", pid).Run()
		}
		// 等待端口释放
		for i := 0; i < 10; i++ {
			time.Sleep(300 * time.Millisecond)
			out, _ := exec.Command("lsof", "-i", fmt.Sprintf(":%d", port), "-sTCP:LISTEN", "-t").Output()
			if len(out) == 0 {
				break
			}
		}
	}

	// 3. 重启子app监听同一端口
	cmdArgs := append(args, portArgName, fmt.Sprintf("%d", port))
	cmd := exec.Command(appPath, cmdArgs...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Start()
}
