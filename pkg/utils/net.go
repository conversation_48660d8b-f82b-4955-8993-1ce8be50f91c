// Package utils -----------------------------
// @file      : net.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/11 17:31
// -------------------------------------------
package utils

import (
	"context"
	"fmt"
	"k8s.io/apimachinery/pkg/util/rand"
	"net"
	"strconv"
)

// FindFreePort 查找空闲端口
func FindFreePort(startPort int) (int, error) {
	for i := 0; i < 100; i++ {
		port := rand.IntnRange(startPort, 65535)
		address := ":" + strconv.Itoa(port)

		listener, err := net.Listen("tcp", address)
		if err != nil {
			continue
		}
		defer listener.Close()
		return port, nil
	}

	// 尝试了100次仍未找到空闲端口
	return startPort, fmt.Errorf("unable to find a free port after 100 attempts")
}

func CreateListener(network, addr string, config net.ListenConfig) (net.Listener, int, error) {
	if len(network) == 0 {
		network = "tcp"
	}

	ln, err := config.Listen(context.TODO(), network, addr)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to listen on %v: %v", addr, err)
	}

	// get port
	tcpAddr, ok := ln.Addr().(*net.TCPAddr)
	if !ok {
		ln.Close()
		return nil, 0, fmt.Errorf("invalid listen address: %q", ln.Addr().String())
	}

	return ln, tcpAddr.Port, nil
}
