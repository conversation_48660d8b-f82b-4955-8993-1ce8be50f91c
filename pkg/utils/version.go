// Package utils -----------------------------
// @file      : version.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/13 16:47
// -------------------------------------------
package utils

import "github.com/hashicorp/go-version"

// VersionCompare 比较版本号
// 如果 v1 > v2 返回true, 否则返回false
func VersionCompare(v1 string, v2 string) (bool, error) {
	left, err := version.NewVersion(v1)
	if err != nil {
		return false, err
	}

	right, err := version.NewVersion(v2)
	if err != nil {
		return false, err
	}

	return left.GreaterThan(right), nil
}

// VersionEqual 判断版本是否相等
func VersionEqual(v1 string, v2 string) (bool, error) {
	left, err := version.NewVersion(v1)
	if err != nil {
		return false, err
	}

	right, err := version.NewVersion(v2)
	if err != nil {
		return false, err
	}

	return left.Equal(right), nil
}
