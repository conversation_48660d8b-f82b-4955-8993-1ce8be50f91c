package utils

import (
	"os/exec"
	"time"
)

/*
#cgo CFLAGS: -x objective-c
#cgo darwin LDFLAGS: -framework CoreGraphics -framework ApplicationServices -framework Cocoa
#include <CoreGraphics/CoreGraphics.h>
#include <Cocoa/Cocoa.h>
#include <ApplicationServices/ApplicationServices.h>

void openAccessibilityPreferencePane() {
    NSString *urlString = @"x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility";
    NSURL *url = [NSURL URLWithString:urlString];
    [[NSWorkspace sharedWorkspace] openURL:url];
}

bool requestAccessibilityPermission() {
    CFStringRef key = CFSTR("AXTrustedCheckOptionPrompt");
    CFBooleanRef value = kCFBooleanTrue;

    const void *keys[] = { key };
    const void *values[] = { value };

    CFDictionaryRef options = CFDictionaryCreate(
        kCFAllocatorDefault,
        keys,
        values,
        1,
        &kCFCopyStringDictionaryKeyCallBacks,
        &kCFTypeDictionaryValueCallBacks
    );

    bool trusted = AXIsProcessTrustedWithOptions(options);
    CFRelease(options);

    return trusted;
}
*/
import "C"

func HasScreenCapturePermission() bool {
	return bool(C.CGPreflightScreenCaptureAccess())
}

// 检查辅助功能权限（通过 AppleScript 检查“辅助功能”列表中是否有本进程）
func HasAccessibilityPermission() bool {
	return C.AXIsProcessTrusted() == 1

	// script := `
	// tell application "System Events"
	//     set uiEnabled to UI elements enabled
	// end tell
	// return uiEnabled
	// `
	// out, err := exec.Command("osascript", "-e", script).Output()
	// if err != nil {
	// 	return false
	// }
	// return strings.TrimSpace(string(out)) == "true"
}

// RequestAccessibilityPermission 请求辅助功能权限
func RequestAccessibilityPermission() {
	C.requestAccessibilityPermission()
}

// RequestScreenCapturePermission 请求屏幕录制权限
func RequestScreenCapturePermission() {
	C.CGRequestScreenCaptureAccess()
}

// 打开系统设置的辅助功能和屏幕录制页面
func OpenAccessibilitySettings() error {
	C.requestAccessibilityPermission()
	time.Sleep(1 * time.Second)
	return exec.Command("open", "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility").Start()
}

// 打开系统设置的辅助功能和屏幕录制页面
func OpenScreenCaptureSettings() error {
	C.CGRequestScreenCaptureAccess()
	time.Sleep(1 * time.Second)
	return exec.Command("open", "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture").Start()
}

// 首次权限检测与请求
func FirstTimePermissionCheckAndRequest() {
	flag := false
	if !HasAccessibilityPermission() {
		OpenAccessibilitySettings()
		flag = true
	}
	if !HasScreenCapturePermission() {
		if flag {
			time.Sleep(7 * time.Second)
		}
		OpenScreenCaptureSettings()
	}
}

// // 获取应用路径
// func getAppPath() string {
// 	exe, _ := os.Executable()
// 	appPath := filepath.Join(filepath.Dir(exe), "../../..")
// 	absPath, _ := filepath.Abs(appPath)
// 	return absPath
// }

// // 获取应用名
// func getAppName() string {
// 	appPath := getAppPath()
// 	base := filepath.Base(appPath)
// 	return strings.TrimSuffix(base, ".app")
// }

// // 提示用户重启
// func promptUserToRestart() {
// 	script := `display dialog "请在系统设置中授权辅助功能和屏幕录制权限后点击重新启动。" buttons {"退出", "重新启动"} default button 2`
// 	out, err := exec.Command("osascript", "-e", script).Output()
// 	if err == nil && strings.Contains(string(out), "重新启动") {
// 		restartApp()
// 	}
// }

// // 重启应用
// func restartApp() {
// 	appPath := getAppPath()
// 	appName := getAppName()
// 	script := fmt.Sprintf(`
//         delay 1
//         tell application "%s" to quit
//         delay 1
//         do shell script "open '%s'"
//     `, appName, appPath)
// 	exec.Command("osascript", "-e", script).Start()
// 	os.Exit(0)
// }

// func main() {
// 	fmt.Println("检查权限中...")
// 	if HasAccessibilityPermission() && HasScreenCapturePermission() {
// 		fmt.Println("✅ 所有权限已就绪")
// 		return
// 	}
// 	fmt.Println("⚠️ 缺少权限，引导用户配置...")
// 	firstTimePermissionCheckAndRequest()
// 	promptUserToRestart()
// }
