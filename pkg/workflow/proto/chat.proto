syntax = "proto3";
package chat;
service ChatService {
  rpc Chat (stream ChatRequest) returns (stream ChatReply);
}

enum CommandType {
    START = 0;
    STOP = 1;
    RESUME = 2;
    END =3;
}

message ChatRequest {
  string conversation_id = 1;
  map<string, string> parameters = 2;
  string prompt = 3;
  CommandType command = 4;
}

message TextResponse {
  string content = 1;
}
message ErrorResponse {
  string content = 1;
  string step_id = 2;
}
message ActionResponse {
  string goal = 1;
  string action = 2;
  string state = 3;        // 成功，失败 
  string step_id = 4;
  string result = 5;   // action结果
  string image_dir = 6;    //  图片
}

message ChatReply {
  string conversation_id = 1;
  oneof response {
    TextResponse text_response = 2;   // 文本响应
    ActionResponse action_response = 3; // 单个动作的信息
    ErrorResponse error_response = 4; // 错误响应
  }
  string msg_type = 5;
  bool is_end = 6;  // 是否结束
}