version: v2
inputs:
  - directory: proto
plugins:
  - remote: buf.build/protocolbuffers/go:v1.36.6
    out: gen
    opt:
      - paths=source_relative
  - remote: buf.build/grpc/go:v1.5.1
    out: gen
    opt:
      - paths=source_relative
managed:
  enabled: true
  override:
    - file_option: go_package_prefix
      value: github.com/LB-AgenticAI/AgenticAI-Client/pkg/workflow/gen
  disable:
    - file_option: go_package
      module: buf.build/bufbuild/protovalidate
