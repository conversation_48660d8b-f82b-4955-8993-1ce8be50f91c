// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: chat.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommandType int32

const (
	CommandType_START  CommandType = 0
	CommandType_STOP   CommandType = 1
	CommandType_RESUME CommandType = 2
	CommandType_END    CommandType = 3
)

// Enum value maps for CommandType.
var (
	CommandType_name = map[int32]string{
		0: "START",
		1: "STOP",
		2: "RESUME",
		3: "END",
	}
	CommandType_value = map[string]int32{
		"START":  0,
		"STOP":   1,
		"RESUME": 2,
		"END":    3,
	}
)

func (x CommandType) Enum() *CommandType {
	p := new(CommandType)
	*p = x
	return p
}

func (x CommandType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommandType) Descriptor() protoreflect.EnumDescriptor {
	return file_chat_proto_enumTypes[0].Descriptor()
}

func (CommandType) Type() protoreflect.EnumType {
	return &file_chat_proto_enumTypes[0]
}

func (x CommandType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommandType.Descriptor instead.
func (CommandType) EnumDescriptor() ([]byte, []int) {
	return file_chat_proto_rawDescGZIP(), []int{0}
}

type ChatRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ConversationId string                 `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Parameters     map[string]string      `protobuf:"bytes,2,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Prompt         string                 `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	Command        CommandType            `protobuf:"varint,4,opt,name=command,proto3,enum=chat.CommandType" json:"command,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ChatRequest) Reset() {
	*x = ChatRequest{}
	mi := &file_chat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatRequest) ProtoMessage() {}

func (x *ChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_chat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatRequest.ProtoReflect.Descriptor instead.
func (*ChatRequest) Descriptor() ([]byte, []int) {
	return file_chat_proto_rawDescGZIP(), []int{0}
}

func (x *ChatRequest) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *ChatRequest) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ChatRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *ChatRequest) GetCommand() CommandType {
	if x != nil {
		return x.Command
	}
	return CommandType_START
}

type TextResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TextResponse) Reset() {
	*x = TextResponse{}
	mi := &file_chat_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextResponse) ProtoMessage() {}

func (x *TextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chat_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextResponse.ProtoReflect.Descriptor instead.
func (*TextResponse) Descriptor() ([]byte, []int) {
	return file_chat_proto_rawDescGZIP(), []int{1}
}

func (x *TextResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ErrorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	StepId        string                 `protobuf:"bytes,2,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	mi := &file_chat_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chat_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_chat_proto_rawDescGZIP(), []int{2}
}

func (x *ErrorResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ErrorResponse) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

type ActionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Goal          string                 `protobuf:"bytes,1,opt,name=goal,proto3" json:"goal,omitempty"`
	Action        string                 `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	State         string                 `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"` // 成功，失败
	StepId        string                 `protobuf:"bytes,4,opt,name=step_id,json=stepId,proto3" json:"step_id,omitempty"`
	Result        string                 `protobuf:"bytes,5,opt,name=result,proto3" json:"result,omitempty"`                     // action结果
	ImageDir      string                 `protobuf:"bytes,6,opt,name=image_dir,json=imageDir,proto3" json:"image_dir,omitempty"` //  图片
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionResponse) Reset() {
	*x = ActionResponse{}
	mi := &file_chat_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionResponse) ProtoMessage() {}

func (x *ActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_chat_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionResponse.ProtoReflect.Descriptor instead.
func (*ActionResponse) Descriptor() ([]byte, []int) {
	return file_chat_proto_rawDescGZIP(), []int{3}
}

func (x *ActionResponse) GetGoal() string {
	if x != nil {
		return x.Goal
	}
	return ""
}

func (x *ActionResponse) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ActionResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *ActionResponse) GetStepId() string {
	if x != nil {
		return x.StepId
	}
	return ""
}

func (x *ActionResponse) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *ActionResponse) GetImageDir() string {
	if x != nil {
		return x.ImageDir
	}
	return ""
}

type ChatReply struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ConversationId string                 `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	// Types that are valid to be assigned to Response:
	//
	//	*ChatReply_TextResponse
	//	*ChatReply_ActionResponse
	//	*ChatReply_ErrorResponse
	Response      isChatReply_Response `protobuf_oneof:"response"`
	MsgType       string               `protobuf:"bytes,5,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	IsEnd         bool                 `protobuf:"varint,6,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"` // 是否结束
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatReply) Reset() {
	*x = ChatReply{}
	mi := &file_chat_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatReply) ProtoMessage() {}

func (x *ChatReply) ProtoReflect() protoreflect.Message {
	mi := &file_chat_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatReply.ProtoReflect.Descriptor instead.
func (*ChatReply) Descriptor() ([]byte, []int) {
	return file_chat_proto_rawDescGZIP(), []int{4}
}

func (x *ChatReply) GetConversationId() string {
	if x != nil {
		return x.ConversationId
	}
	return ""
}

func (x *ChatReply) GetResponse() isChatReply_Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *ChatReply) GetTextResponse() *TextResponse {
	if x != nil {
		if x, ok := x.Response.(*ChatReply_TextResponse); ok {
			return x.TextResponse
		}
	}
	return nil
}

func (x *ChatReply) GetActionResponse() *ActionResponse {
	if x != nil {
		if x, ok := x.Response.(*ChatReply_ActionResponse); ok {
			return x.ActionResponse
		}
	}
	return nil
}

func (x *ChatReply) GetErrorResponse() *ErrorResponse {
	if x != nil {
		if x, ok := x.Response.(*ChatReply_ErrorResponse); ok {
			return x.ErrorResponse
		}
	}
	return nil
}

func (x *ChatReply) GetMsgType() string {
	if x != nil {
		return x.MsgType
	}
	return ""
}

func (x *ChatReply) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

type isChatReply_Response interface {
	isChatReply_Response()
}

type ChatReply_TextResponse struct {
	TextResponse *TextResponse `protobuf:"bytes,2,opt,name=text_response,json=textResponse,proto3,oneof"` // 文本响应
}

type ChatReply_ActionResponse struct {
	ActionResponse *ActionResponse `protobuf:"bytes,3,opt,name=action_response,json=actionResponse,proto3,oneof"` // 单个动作的信息
}

type ChatReply_ErrorResponse struct {
	ErrorResponse *ErrorResponse `protobuf:"bytes,4,opt,name=error_response,json=errorResponse,proto3,oneof"` // 错误响应
}

func (*ChatReply_TextResponse) isChatReply_Response() {}

func (*ChatReply_ActionResponse) isChatReply_Response() {}

func (*ChatReply_ErrorResponse) isChatReply_Response() {}

var File_chat_proto protoreflect.FileDescriptor

const file_chat_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"chat.proto\x12\x04chat\"\xfd\x01\n" +
	"\vChatRequest\x12'\n" +
	"\x0fconversation_id\x18\x01 \x01(\tR\x0econversationId\x12A\n" +
	"\n" +
	"parameters\x18\x02 \x03(\v2!.chat.ChatRequest.ParametersEntryR\n" +
	"parameters\x12\x16\n" +
	"\x06prompt\x18\x03 \x01(\tR\x06prompt\x12+\n" +
	"\acommand\x18\x04 \x01(\x0e2\x11.chat.CommandTypeR\acommand\x1a=\n" +
	"\x0fParametersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"(\n" +
	"\fTextResponse\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\"B\n" +
	"\rErrorResponse\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x17\n" +
	"\astep_id\x18\x02 \x01(\tR\x06stepId\"\xa0\x01\n" +
	"\x0eActionResponse\x12\x12\n" +
	"\x04goal\x18\x01 \x01(\tR\x04goal\x12\x16\n" +
	"\x06action\x18\x02 \x01(\tR\x06action\x12\x14\n" +
	"\x05state\x18\x03 \x01(\tR\x05state\x12\x17\n" +
	"\astep_id\x18\x04 \x01(\tR\x06stepId\x12\x16\n" +
	"\x06result\x18\x05 \x01(\tR\x06result\x12\x1b\n" +
	"\timage_dir\x18\x06 \x01(\tR\bimageDir\"\xac\x02\n" +
	"\tChatReply\x12'\n" +
	"\x0fconversation_id\x18\x01 \x01(\tR\x0econversationId\x129\n" +
	"\rtext_response\x18\x02 \x01(\v2\x12.chat.TextResponseH\x00R\ftextResponse\x12?\n" +
	"\x0faction_response\x18\x03 \x01(\v2\x14.chat.ActionResponseH\x00R\x0eactionResponse\x12<\n" +
	"\x0eerror_response\x18\x04 \x01(\v2\x13.chat.ErrorResponseH\x00R\rerrorResponse\x12\x19\n" +
	"\bmsg_type\x18\x05 \x01(\tR\amsgType\x12\x15\n" +
	"\x06is_end\x18\x06 \x01(\bR\x05isEndB\n" +
	"\n" +
	"\bresponse*7\n" +
	"\vCommandType\x12\t\n" +
	"\x05START\x10\x00\x12\b\n" +
	"\x04STOP\x10\x01\x12\n" +
	"\n" +
	"\x06RESUME\x10\x02\x12\a\n" +
	"\x03END\x10\x032=\n" +
	"\vChatService\x12.\n" +
	"\x04Chat\x12\x11.chat.ChatRequest\x1a\x0f.chat.ChatReply(\x010\x01B\x80\x01\n" +
	"\bcom.chatB\tChatProtoP\x01Z9github.com/LB-AgenticAI/AgenticAI-Client/pkg/workflow/gen\xa2\x02\x03CXX\xaa\x02\x04Chat\xca\x02\x04Chat\xe2\x02\x10Chat\\GPBMetadata\xea\x02\x04Chatb\x06proto3"

var (
	file_chat_proto_rawDescOnce sync.Once
	file_chat_proto_rawDescData []byte
)

func file_chat_proto_rawDescGZIP() []byte {
	file_chat_proto_rawDescOnce.Do(func() {
		file_chat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_chat_proto_rawDesc), len(file_chat_proto_rawDesc)))
	})
	return file_chat_proto_rawDescData
}

var file_chat_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_chat_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_chat_proto_goTypes = []any{
	(CommandType)(0),       // 0: chat.CommandType
	(*ChatRequest)(nil),    // 1: chat.ChatRequest
	(*TextResponse)(nil),   // 2: chat.TextResponse
	(*ErrorResponse)(nil),  // 3: chat.ErrorResponse
	(*ActionResponse)(nil), // 4: chat.ActionResponse
	(*ChatReply)(nil),      // 5: chat.ChatReply
	nil,                    // 6: chat.ChatRequest.ParametersEntry
}
var file_chat_proto_depIdxs = []int32{
	6, // 0: chat.ChatRequest.parameters:type_name -> chat.ChatRequest.ParametersEntry
	0, // 1: chat.ChatRequest.command:type_name -> chat.CommandType
	2, // 2: chat.ChatReply.text_response:type_name -> chat.TextResponse
	4, // 3: chat.ChatReply.action_response:type_name -> chat.ActionResponse
	3, // 4: chat.ChatReply.error_response:type_name -> chat.ErrorResponse
	1, // 5: chat.ChatService.Chat:input_type -> chat.ChatRequest
	5, // 6: chat.ChatService.Chat:output_type -> chat.ChatReply
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_chat_proto_init() }
func file_chat_proto_init() {
	if File_chat_proto != nil {
		return
	}
	file_chat_proto_msgTypes[4].OneofWrappers = []any{
		(*ChatReply_TextResponse)(nil),
		(*ChatReply_ActionResponse)(nil),
		(*ChatReply_ErrorResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_chat_proto_rawDesc), len(file_chat_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_chat_proto_goTypes,
		DependencyIndexes: file_chat_proto_depIdxs,
		EnumInfos:         file_chat_proto_enumTypes,
		MessageInfos:      file_chat_proto_msgTypes,
	}.Build()
	File_chat_proto = out.File
	file_chat_proto_goTypes = nil
	file_chat_proto_depIdxs = nil
}
