// Package message -----------------------------
// @file      : message.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 16:14
// -------------------------------------------
package message

import (
	pb "AgenticAI-Client/pkg/workflow/gen"

	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
)

type Object struct {
}

func NewTaskRequest(cid, prompt, deviceID string, ct emqx.CommandType, isQuit bool, permissionValue string) *TaskRequest {
	rsp := &TaskRequest{
		IsQuit: isQuit,
		Payload: pb.ChatRequest{
			ConversationId: cid,
			Parameters: map[string]string{
				DeviceIDKey: deviceID,
			},
			Prompt:  prompt,
			Command: pb.CommandType(ct),
		},
	}

	if len(permissionValue) > 0 {
		rsp.Payload.Parameters[DevicePermissionKey] = permissionValue
	}

	return rsp
}

func PermissionRequest(permissionValue string) *pb.ChatRequest {
	req := &pb.ChatRequest{
		Parameters: map[string]string{
			DevicePermissionKey: permissionValue,
		},
	}

	return req
}

func EMQXTaskRequestToTaskRequest(taskReq *emqx.TaskRequest) *TaskRequest {
	if taskReq == nil {
		return &TaskRequest{}
	}
	rsp := &TaskRequest{
		IsQuit: false,
		Payload: pb.ChatRequest{
			ConversationId: taskReq.TaskId,
			Prompt:         taskReq.Prompt,
			Command:        pb.CommandType(taskReq.Command),
		},
		EMQXPayload: *taskReq,
	}

	return rsp
}

const (
	// agent permission
	// key
	DeviceIDKey         = "deviceID"
	DevicePermissionKey = "permission"

	// value
	DevicePermissionValueCheck    = "check"
	DevicePermissionValueRequest  = "request"
	DevicePermissionValueToAccess = "navigate_accessibility"
	DevicePermissionValueToScreen = "navigate_screenshot"
)

type MsgType uint8

const (
	MsgTypeTextResponse MsgType = iota
	MsgTypeActionResponse
	MsgTypeErrorResponse
)

type TaskRequest struct {
	IsQuit      bool
	Payload     pb.ChatRequest
	EMQXPayload emqx.TaskRequest
}

type TaskResponse struct {
	IsQuit  bool
	Payload emqx.TaskResponse
}

const (
	SubMsgTypeNotify     = "notify"
	SubMsgTypePlan       = "plan"
	SubMsgTypePermission = "permission"
	SubMsgTypeTaskResult = "task_result"

	SubMsgTypeStep   = "step"
	SubMsgTypeAction = "action"

	SubMsgTypeError           = "error"
	SubMsgTypePermissionError = "permission_error"
)

func GetMsgType(input string) MsgType {
	if input == SubMsgTypeNotify || input == SubMsgTypePlan || input == SubMsgTypePermission || input == SubMsgTypeTaskResult {
		return MsgTypeTextResponse
	}
	if input == SubMsgTypeStep || input == SubMsgTypeAction {
		return MsgTypeActionResponse
	}
	return MsgTypeErrorResponse
}
