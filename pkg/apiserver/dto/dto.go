// Package dto -----------------------------
// @file      : dto.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/22 17:00
// -------------------------------------------
package dto

type Null struct {
}

type PermissionCheckResponse struct {
	AccessibilityPermission bool `json:"accessibility_permission"`
	ScreenshootPermission   bool `json:"screenshoot_permission"`
}

type LoginReq struct {
	Code  string `json:"code"`
	State string `json:"state"`
}

type LoginRsp struct {
}

type LogoutReq struct {
}

type LogoutRsp struct {
}
