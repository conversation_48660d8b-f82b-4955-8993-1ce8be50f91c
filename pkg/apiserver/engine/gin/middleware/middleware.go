package middleware

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"time"
)

var Middlewares = []gin.HandlerFunc{gin.Recovery(), Error<PERSON>andler()}

func Logger<PERSON>andler() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		str := fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
		return str
	})
}

func ErrorHandler() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Next()

		// check gin return error
		for _, e := range ctx.Errors {
			err := e.Err
			// not our define error
			// print in server log and not tell client error detail
			zap.L().Error(err.Error())
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "server error",
			})

			return
		}
	}
}
