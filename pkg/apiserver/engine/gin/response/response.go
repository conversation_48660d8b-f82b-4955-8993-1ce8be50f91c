package response

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

func JsonResponse(ctx *gin.Context, module string, data interface{}, err error) {
	if err != nil {
		ctx.Error(err)
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0, "msg": "Success", "data": data, "module": module})
}

func CheckError(ctx *gin.Context, code int, module, msg string, err error) {
	if err != nil {
		zap.L().Error(msg, zap.Int("code", code), zap.String("module", module))
		ctx.JSON(code, gin.H{"code": code, "msg": msg, "module": module})
		panic(err)
	}
}

func FailWithMsg(ctx *gin.Context, code int, module, msg string) {
	zap.L().<PERSON>rror(msg)
	ctx.Abort()
	ctx.JSON(code, gin.H{"code": code, "msg": msg, "module": module})
}
