package gin

import (
	"AgenticAI-Client/internal/global"
	"AgenticAI-Client/pkg/apiserver/engine"
	"AgenticAI-Client/pkg/apiserver/engine/gin/middleware"
	"AgenticAI-Client/pkg/apiserver/handler"
	"github.com/gin-gonic/gin"
	"io"
)

type Engine struct {
	*gin.Engine
	userService       handler.UserServiceHandler
	permissionService handler.PermissionServiceHandler
}

func NewGinEngine(us handler.UserServiceHandler, ps handler.PermissionServiceHandler) engine.Interface {
	e := &Engine{
		Engine:            createEngine(),
		userService:       us,
		permissionService: ps,
	}

	return e
}

func createEngine() *gin.Engine {
	isDev := global.IsDev()
	if !isDev {
		gin.SetMode(gin.ReleaseMode)
		gin.DefaultWriter = io.Discard
	}

	e := gin.New()
	if isDev {
		e.Use(middleware.LoggerHandler())
	}

	// register middleware
	e.Use(middleware.Middlewares...)

	return e
}
