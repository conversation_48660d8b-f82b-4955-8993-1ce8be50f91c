// Package handler -----------------------------
// @file      : interface.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/22 16:20
// -------------------------------------------
package handler

import (
	"AgenticAI-Client/pkg/apiserver/dto"
	"context"
)

type UserServiceHandler interface {
	LoginHandler(context.Context, *dto.LoginReq) (*dto.LoginRsp, error)
	LogoutHandler(context.Context, *dto.LogoutReq) (*dto.LogoutRsp, error)
}

type PermissionServiceHandler interface {
	PermissionCheck(context.Context, *dto.Null) (*dto.PermissionCheckResponse, error)
	PermissionRequest(context.Context, *dto.Null) (string, error)
	PermissionToAccess(context.Context, *dto.Null) (string, error)
	PermissionToScreen(context.Context, *dto.Null) (string, error)
}
