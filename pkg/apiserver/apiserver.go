// Package apiserver -----------------------------
// @file      : apiserver.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/22 16:14
// -------------------------------------------
package apiserver

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/apiserver/engine"
	"AgenticAI-Client/pkg/apiserver/engine/gin"
	"AgenticAI-Client/pkg/apiserver/handler"
	"AgenticAI-Client/pkg/utils"
	"context"
	"crypto/tls"
	"fmt"
	"go.uber.org/zap"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"net"
	"net/http"
	"strconv"
	"time"
)

type Interface interface {
	Run()
}

type impl struct {
	ctx      context.Context
	cfg      *config.ServerConfig
	server   *http.Server
	listener net.Listener
	engine   engine.Interface
}

func (i *impl) Run() {
	// run api server
	shutdownCh, _, err := RunServer(i.server, i.listener, i.cfg.RequestTimeout, i.ctx.Done())
	if err != nil {
		panic(err)
	}
	<-shutdownCh
}

func New(ctx context.Context, cfg *config.ServerConfig, usd handler.UserServiceHandler, psd handler.PermissionServiceHandler) (Interface, error) {
	addr := net.JoinHostPort(cfg.Addr, strconv.Itoa(cfg.Port))
	ln, _, err := utils.CreateListener("tcp", addr, net.ListenConfig{})
	if err != nil {
		return nil, err
	}

	ginEngine := gin.NewGinEngine(usd, psd)
	hdn := ginEngine.CreateHandler()
	srv := &http.Server{
		Addr:           ln.Addr().String(),
		Handler:        hdn,
		ReadTimeout:    10 * time.Second,
		WriteTimeout:   10 * time.Second,
		MaxHeaderBytes: 1 << 20,
	}

	_apiServer := &impl{
		ctx:      ctx,
		cfg:      cfg,
		server:   srv,
		listener: ln,
		engine:   ginEngine,
	}

	return _apiServer, nil
}

const (
	defaultKeepAlivePeriod = 3 * time.Minute
)

// RunServer spawns a go-routine continuously serving until the stopCh is
// closed.
// It returns a stoppedCh that is closed when all non-hijacked active requests
// have been processed.
// This function does not block
// TODO: make private when insecure serving is gone from the kube-apiserver
func RunServer(
	server *http.Server,
	ln net.Listener,
	shutDownTimeout time.Duration,
	stopCh <-chan struct{},
) (<-chan struct{}, <-chan struct{}, error) {
	if ln == nil {
		return nil, nil, fmt.Errorf("listener must not be nil")
	}

	// Shutdown server gracefully.
	serverShutdownCh, listenerStoppedCh := make(chan struct{}), make(chan struct{})
	go func() {
		defer close(serverShutdownCh)
		<-stopCh
		ctx, cancel := context.WithTimeout(context.Background(), shutDownTimeout)
		server.Shutdown(ctx)
		cancel()
	}()

	go func() {
		defer utilruntime.HandleCrash()
		defer close(listenerStoppedCh)

		var listener net.Listener
		listener = tcpKeepAliveListener{ln}
		if server.TLSConfig != nil {
			listener = tls.NewListener(listener, server.TLSConfig)
		}

		err := server.Serve(listener)

		msg := fmt.Sprintf("Stopped listening on %s", ln.Addr().String())
		select {
		case <-stopCh:
			zap.L().Info(msg)
		default:
			panic(fmt.Sprintf("%s due to error: %v", msg, err))
		}
	}()

	return serverShutdownCh, listenerStoppedCh, nil
}

// tcpKeepAliveListener sets TCP keep-alive timeouts on accepted
// connections. It's used by ListenAndServe and ListenAndServeTLS so
// dead TCP connections (e.g. closing laptop mid-download) eventually
// go away.
//
// Copied from Go 1.7.2 net/http/server.go
type tcpKeepAliveListener struct {
	net.Listener
}

func (ln tcpKeepAliveListener) Accept() (net.Conn, error) {
	c, err := ln.Listener.Accept()
	if err != nil {
		return nil, err
	}
	if tc, ok := c.(*net.TCPConn); ok {
		tc.SetKeepAlive(true)
		tc.SetKeepAlivePeriod(defaultKeepAlivePeriod)
	}
	return c, nil
}
