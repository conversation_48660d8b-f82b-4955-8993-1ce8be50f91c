// Package launcher -----------------------------
// @file      : launcher.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/14 10:49
// -------------------------------------------
package launcher

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/service"
	"go.uber.org/zap"
)

var loggerModule = zap.String("module", "launcher")

func Start(F func()) {
	defer F()

	c := config.GetTuriXConfig()
	uss := service.NewUpdateService(c)
	usi := uss.GetUpdateStatus()
	if usi == nil {
		zap.L().With(loggerModule).Fatal("Failed to get update info")
		return
	}

	if !usi.PendingAgent && !usi.PendingApp {
		zap.L().With(loggerModule).Info("Not Pending, skip update")
		return
	}

	if usi.PendingAgent {
		// TODO: load and update agent
		usi.PendingAgent = false
		err := uss.SetUpdateStatus(usi)
		if err != nil {
			zap.L().With(loggerModule).Error("Failed to SetUpdateStatus", zap.Error(err))
			return
		}
		zap.L().With(loggerModule).Info("Update agent successfully")
	}

	if usi.PendingApp {
		// TODO: load and update app
		usi.PendingApp = false
		err := uss.SetUpdateStatus(usi)
		if err != nil {
			zap.L().With(loggerModule).Error("Failed to SetUpdateStatus", zap.Error(err))
			return
		}
		zap.L().With(loggerModule).Info("Update app successfully")
	}
}
