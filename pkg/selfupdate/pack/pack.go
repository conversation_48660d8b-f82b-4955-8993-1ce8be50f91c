// Package pack -----------------------------
// @file      : pack.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/12 09:58
// -------------------------------------------
package pack

import (
	"archive/tar"
	"compress/gzip"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// CompressOptions 定义压缩选项
type CompressOptions struct {
	ExcludePatterns  []string     // 要排除的文件模式(如.git, *.tmp)
	IncludeHidden    bool         // 是否包含隐藏文件
	CompressionLevel int          // 压缩级别(gzip.DefaultCompression等)
	ProgressFunc     func(string) // 进度回调函数
}

// CompressDirToTarGz 将目录压缩为tar.gz文件
// srcDir: 要压缩的源目录路径
// destFile: 目标文件路径(.tar.gz扩展名可选)
// options: 压缩选项，可为nil使用默认值
// 返回: 错误信息
func CompressDirToTarGz(srcDir, destFile string, options *CompressOptions) error {
	// 规范化输入路径
	srcDir = filepath.Clean(srcDir)
	destFile = filepath.Clean(destFile)

	// 设置默认选项
	if options == nil {
		options = &CompressOptions{}
	}
	if options.CompressionLevel == 0 {
		options.CompressionLevel = gzip.DefaultCompression
	}

	// 确保目标文件有正确的扩展名
	if !strings.HasSuffix(destFile, ".tar.gz") && !strings.HasSuffix(destFile, ".tgz") {
		destFile += ".tar.gz"
	}

	// 检查源目录是否存在
	srcInfo, err := os.Stat(srcDir)
	if err != nil {
		return fmt.Errorf("源目录检查失败: %w", err)
	}
	if !srcInfo.IsDir() {
		return errors.New("源路径不是目录")
	}

	// 创建目标文件
	outFile, err := os.Create(destFile)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %w", err)
	}
	defer outFile.Close()

	// 创建gzip写入器
	gzipWriter, err := gzip.NewWriterLevel(outFile, options.CompressionLevel)
	if err != nil {
		return fmt.Errorf("创建gzip写入器失败: %w", err)
	}
	defer gzipWriter.Close()

	// 创建tar写入器
	tarWriter := tar.NewWriter(gzipWriter)
	defer tarWriter.Close()

	// 遍历目录并添加到tar包
	return filepath.Walk(srcDir, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 获取相对路径
		relPath, err := filepath.Rel(srcDir, filePath)
		if err != nil {
			return fmt.Errorf("获取相对路径失败: %w", err)
		}

		// 跳过根目录
		if relPath == "." {
			return nil
		}

		// 检查排除模式
		for _, pattern := range options.ExcludePatterns {
			match, err := filepath.Match(pattern, filepath.Base(filePath))
			if err != nil {
				return fmt.Errorf("模式匹配错误: %w", err)
			}
			if match {
				if info.IsDir() {
					return filepath.SkipDir
				}
				return nil
			}
		}

		// 跳过隐藏文件(除非明确要求包含)
		if !options.IncludeHidden && strings.HasPrefix(filepath.Base(filePath), ".") {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// 调用进度回调
		if options.ProgressFunc != nil {
			options.ProgressFunc(relPath)
		}

		// 创建tar头信息
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			return fmt.Errorf("创建tar头信息失败: %w", err)
		}

		// 确保路径使用正斜杠(跨平台兼容)
		header.Name = filepath.ToSlash(relPath)

		// 写入头信息
		if err := tarWriter.WriteHeader(header); err != nil {
			return fmt.Errorf("写入tar头信息失败: %w", err)
		}

		// 如果是目录，没有内容需要写入
		if info.IsDir() {
			return nil
		}

		// 打开文件
		file, err := os.Open(filePath)
		if err != nil {
			return fmt.Errorf("打开文件失败: %w", err)
		}
		defer file.Close()

		// 将文件内容复制到tar包
		if _, err := io.Copy(tarWriter, file); err != nil {
			return fmt.Errorf("写入文件内容失败: %w", err)
		}

		return nil
	})
}
