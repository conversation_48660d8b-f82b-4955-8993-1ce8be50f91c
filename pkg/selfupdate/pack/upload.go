// Package pack-----------------------------
// @file      : upload.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/12 14:24
// -------------------------------------------
package pack

import (
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/pkg/oss"
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"
)

type Manifest struct {
	Version      string `json:"version"`
	Sha256       string `json:"sha256"`
	Description  string `json:"description"`
	Size         string `json:"size"`
	Url          string `json:"url"`
	Arch         string `json:"arch"`
	Name         string `json:"name"`
	UpdateTime   string `json:"update_time"`
	UpdateTimeTS int64  `json:"update_time_ts"`
}

func generateSha256(path string) (string, error) {
	h := sha256.New()

	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	_, err = io.Copy(h, file)
	if err != nil {
		return "", err
	}

	// 返回Base64字符串
	//return base64.StdEncoding.EncodeToString(h.Sum(nil)), nil

	// 返回十六进制字符串(与sha256sum一致)
	return fmt.Sprintf("%x", h.Sum(nil)), nil
}

func UploadToAliCloudOSS(source, version, platform, appName string) error {
	c := &oss.Config{
		Endpoint:        constant.DefaultOSSEndpoint,
		AccessKeyID:     constant.DefaultOSSAccessKeyID,
		AccessKeySecret: constant.DefaultOSSAccessKeySecret,
		BucketName:      constant.DefaultOSSBucketName,
		URLPrefix:       constant.DefaultOSSUrlPrefix,
		Timeout:         constant.DefaultOSSTimeout,
		ExpireTime:      constant.DefaultOSSExpireTime,
		IsPrivate:       false,
	}

	client, err := oss.NewClient(c)
	if err != nil {
		return err
	}

	_type := "release"
	if strings.Index(version, "alpha") != -1 {
		_type = "test"
	}

	// upload app
	app := filepath.Join(source, appName+".app.zip")
	fmt.Printf("开始上传[%s]\n", app)
	res, err := client.UploadFile(context.Background(), app, fmt.Sprintf("%s/%s/%s/%s", _type, appName, version, filepath.Base(app)))
	if err != nil {
		return err
	}
	sizeMB := fmt.Sprintf("%.2fMB", float64(res.Size)/(1024*1024))
	fmt.Printf("上传[%s]至OSS成功, url: %s, size: %s, objectKey: %s\n", res.Filename, res.URL, sizeMB, res.ObjectKey)

	// generate sha256 checksum
	hash, err := generateSha256(app)
	if err != nil {
		panic(err)
	}
	// generate manifest
	now := time.Now()
	_manifest := Manifest{
		Version:      version,
		Sha256:       hash,
		Description:  ``,
		Size:         sizeMB,
		Url:          res.URL,
		Name:         appName,
		Arch:         platform,
		UpdateTime:   now.Format(time.DateTime),
		UpdateTimeTS: now.UnixNano(),
	}
	b, err := json.MarshalIndent(_manifest, "", "    ")
	if err != nil {
		return err
	}
	manifest := filepath.Join(source, platform+".json")
	err = os.WriteFile(manifest, b, 0755)
	if err != nil {
		return err
	}

	// upload manifest
	fmt.Printf("开始上传[%s]\n", manifest)
	res, err = client.UploadFile(context.Background(), manifest, fmt.Sprintf("%s/%s/%s", _type, appName, filepath.Base(manifest)))
	if err != nil {
		return err
	}
	fmt.Printf("上传[%s]至OSS成功, url: %s, size: %d, objectKey: %s\n", res.Filename, res.URL, res.Size, res.ObjectKey)

	res, err = client.UploadFile(context.Background(), manifest, fmt.Sprintf("%s/%s/%s/%s", _type, appName, version, filepath.Base(manifest)))
	if err != nil {
		return err
	}
	fmt.Printf("上传[%s]至OSS成功, url: %s, size: %d, objectKey: %s\n", res.Filename, res.URL, res.Size, res.ObjectKey)

	return nil
}
