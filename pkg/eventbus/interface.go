// Package eventbus -----------------------------
// @file      : interface.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/7/18 10:00
// -------------------------------------------
package eventbus

import (
	"AgenticAI-Client/pkg/workflow/message"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
)

// EventBus 替代broker.Interface，使用Wails event系统进行消息传递
type EventBus interface {
	// Run 启动事件总线
	Run()
	// Close 关闭事件总线
	Close()
	
	// 聊天任务相关事件
	// EmitChatResponse 发送聊天响应到前端
	EmitChatResponse(response *message.TaskResponse)
	// RegisterChatRequestHandler 注册聊天请求处理器
	RegisterChatRequestHandler(handler func(request *message.TaskRequest) error)
	// RegisterConnectHandler 注册连接处理器
	RegisterConnectHandler(handler func() error)
	
	// 设备相关事件
	// EmitDeviceStatus 发送设备状态到前端
	EmitDeviceStatus(status *emqx.DeviceResponse)
	// EmitDeviceHeartbeat 发送设备心跳到前端
	EmitDeviceHeartbeat(heartbeat *emqx.DeviceHeartbeatResponse)
	// RegisterSettingRequestHandler 注册设置请求处理器
	RegisterSettingRequestHandler(handler func(request *emqx.DeviceSettingsRequest) error)
	
	// 应用控制相关事件
	// RegisterQuitAppHandler 注册退出应用处理器
	RegisterQuitAppHandler(handler func() error)
	
	// 重连相关（保持兼容性，但实际不需要重连）
	Relink() error
}
