// Package eventbus -----------------------------
// @file      : adapter.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/7/18 10:00
// -------------------------------------------
package eventbus

import (
	"AgenticAI-Client/pkg/broker"
	"AgenticAI-Client/pkg/workflow/message"
	"context"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
)

// BrokerAdapter 适配器，让EventBus实现broker.Interface接口
// 这样可以在迁移过程中保持兼容性
type BrokerAdapter struct {
	eventBus EventBus
}

func NewBrokerAdapter(ctx context.Context) broker.Interface {
	eventBus := NewEventBus(ctx)
	return &BrokerAdapter{
		eventBus: eventBus,
	}
}

func (a *BrokerAdapter) Run() {
	a.eventBus.Run()
}

func (a *BrokerAdapter) Close() {
	a.eventBus.Close()
}

func (a *BrokerAdapter) PublishChatReply(reply *message.TaskResponse) {
	a.eventBus.EmitChatResponse(reply)
}

func (a *BrokerAdapter) RegisterChatRequestHandler(handler func(request *message.TaskRequest) error) {
	a.eventBus.RegisterChatRequestHandler(handler)
}

func (a *BrokerAdapter) Relink() error {
	return a.eventBus.Relink()
}

func (a *BrokerAdapter) RegisterConnectHandler(handler func() error) {
	a.eventBus.RegisterConnectHandler(handler)
}

func (a *BrokerAdapter) PublishDeviceBindingReply(reply *emqx.DeviceResponse) error {
	a.eventBus.EmitDeviceStatus(reply)
	return nil
}

func (a *BrokerAdapter) PublishDeviceHeartbeatReply(reply *emqx.DeviceHeartbeatResponse) error {
	a.eventBus.EmitDeviceHeartbeat(reply)
	return nil
}

func (a *BrokerAdapter) RegisterSettingRequestHandler(handler func(request *emqx.DeviceSettingsRequest) error) {
	a.eventBus.RegisterSettingRequestHandler(handler)
}

func (a *BrokerAdapter) RegisterQuitAppHandler(handler func() error) {
	a.eventBus.RegisterQuitAppHandler(handler)
}
