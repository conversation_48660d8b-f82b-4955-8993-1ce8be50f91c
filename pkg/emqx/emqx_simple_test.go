// Package emqx -----------------------------
// @file      : emqx_simple_test.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/16 16:56
// -------------------------------------------
package emqx

import (
	"AgenticAI-Client/internal/constant"
	"fmt"
	"testing"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

const (
	Topic    = "test/topic"
	ClientID = "testClient"
)

func TestMQTT(t *testing.T) {
	opts := mqtt.NewClientOptions().AddBroker(constant.DefaultEMQXAddr)
	opts.SetClientID(ClientID)
	opts.SetDefaultPublishHandler(messageHandler)

	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		t.Fatalf("Failed to connect to MQTT broker: %v", token.Error())
	}

	// 订阅消息
	if token := client.Subscribe(Topic, 0, nil); token.Wait() && token.Error() != nil {
		t.Fatalf("Failed to subscribe to topic: %v", token.Error())
	}

	// 发布消息
	if token := client.Publish(Topic, 0, false, "Hello, EMQX!"); token.Wait() && token.Error() != nil {
		t.Fatalf("Failed to publish message: %v", token.Error())
	}

	// 等待消息接收
	time.Sleep(2 * time.Second)

	// 取消订阅
	if token := client.Unsubscribe(Topic); token.Wait() && token.Error() != nil {
		t.Fatalf("Failed to unsubscribe from topic: %v", token.Error())
	}

	client.Disconnect(250)
}

// 消息处理函数
func messageHandler(client mqtt.Client, msg mqtt.Message) {
	fmt.Printf("Received message on topic: %s\nMessage: %s\n", msg.Topic(), msg.Payload())
}
