// Package emqx -----------------------------
// @file      : chatTask.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/15 16:21
// -------------------------------------------
package emqx

import (
	"AgenticAI-Client/pkg/service"
	"AgenticAI-Client/pkg/workflow/message"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"
	"k8s.io/apimachinery/pkg/util/json"
)

func (b *Broker) PublishChatReply(reply *message.TaskResponse) {
	reply.Payload.DeviceId = service.UserServiceImpl.GetDeviceID()
	b.sendCh <- reply
}

func (b *Broker) RegisterChatRequestHandler(handler func(request *message.TaskRequest) error) {
	b.chatRequestHandler = handler
}

func (b *Broker) RegisterConnectHandler(handler func() error) {
	b.connectRequestHandler = handler
}

func (b *Broker) publishChatReply(topic string, reply *emqx.TaskResponse, isQuit bool) error {
	if b.client == nil || reply == nil {
		return nil
	}

	data, err := json.Marshal(reply)
	if err != nil {
		b.logger.Error("Failed to marshal emqx.TaskResponse message", zap.Any("reply", *reply), zap.Error(err))
		return err
	}
	token := b.client.Publish(topic, 1, false, data)
	token.Wait()
	if token.Error() != nil {
		b.logger.Error("MQTT publish failed", zap.Any("reply", *reply), zap.Error(token.Error()))
		return token.Error()
	}
	b.logger.Debug("send MQTT message", zap.String("task_id", reply.TaskId), zap.String("plan", reply.Plan), zap.Any("steps", reply.Steps), zap.Bool("is_end", reply.IsEnd), zap.Int64("executed_at", reply.ExecutedAt), zap.String("status", reply.Status))

	if isQuit && b.quitAppHandler != nil {
		err = b.quitAppHandler()
		if err != nil {
			b.logger.Error("quit app failed", zap.Error(err))
		}
	}
	return nil
}

func (b *Broker) receiveCloudRequestHandler(c mqtt.Client, req mqtt.Message) {
	// 解析消息体
	var request emqx.Request
	err := json.Unmarshal(req.Payload(), &request)
	if err != nil {
		b.logger.Error("Error decoding JSON", zap.Error(err))
		return
	}

	// 未传deviceID，或deviceID与本机不匹配，则不处理
	if request.DeviceId != service.UserServiceImpl.GetDeviceID() {
		return
	}

	switch request.Type {
	case emqx.TASK_REQUEST:
		// 处理聊天请求
		var data []byte
		data, err = json.Marshal(request.Data)
		if err != nil {
			b.logger.Error("json marshal task request data", zap.Any("data", request.Data))
			return
		}

		var val emqx.TaskRequest
		err = json.Unmarshal(data, &val)
		if err != nil {
			b.logger.Error("json unmarshal task request failed", zap.Any("data", request.Data))
			return
		}
		b.logger.Debug("receive task request", zap.Any("request", val))
		b.receiveCh <- message.EMQXTaskRequestToTaskRequest(&val)
	case emqx.DEVICE_REQUEST:
		// 处理唤起设备设置请求
		var data []byte
		data, err = json.Marshal(request.Data)
		if err != nil {
			b.logger.Error("json marshal device settings request data", zap.Any("data", request.Data))
			return
		}

		var val emqx.DeviceSettingsRequest
		err = json.Unmarshal(data, &val)
		if err != nil {
			b.logger.Error("json unmarshal device settings request failed", zap.Any("data", request.Data))
			return
		}
		b.logger.Debug("receive device settings request", zap.Any("request", val))
		b.receiveChSetting <- &val
	default:
		b.logger.Error("Unknown request type", zap.Int("type", int(request.Type)))
	}

}
