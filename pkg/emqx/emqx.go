// Package emqx -----------------------------
// @file      : emqx.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 13:59
// -------------------------------------------
package emqx

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/internal/ui/task"
	"AgenticAI-Client/pkg/broker"
	"AgenticAI-Client/pkg/service"
	"AgenticAI-Client/pkg/workflow/message"
	"context"
	"errors"
	"fmt"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"k8s.io/client-go/util/retry"
	"time"
)

type Broker struct {
	ctx                   context.Context
	client                mqtt.Client
	cfg                   *config.EMQXConfig
	receiveCh             chan *message.TaskRequest
	sendCh                chan *message.TaskResponse
	logger                *zap.Logger
	chatRequestHandler    func(request *message.TaskRequest) error
	connectRequestHandler func() error

	receiveChSetting      chan *emqx.DeviceSettingsRequest
	settingRequestHandler func(request *emqx.DeviceSettingsRequest) error
	quitAppHandler        func() error
}

func NewBroker(ctx context.Context, c *config.EMQXConfig) (broker.Interface, error) {
	bk := &Broker{
		ctx:       ctx,
		cfg:       c,
		receiveCh: make(chan *message.TaskRequest, 10),
		sendCh:    make(chan *message.TaskResponse, 10),
		logger:    zap.L().With(zap.String("module", "emqx")),
		chatRequestHandler: func(request *message.TaskRequest) error {
			return nil
		},
		connectRequestHandler: func() error {
			return nil
		},
		receiveChSetting: make(chan *emqx.DeviceSettingsRequest, 10),
		settingRequestHandler: func(request *emqx.DeviceSettingsRequest) error {
			return nil
		},
		quitAppHandler: func() error { return nil },
	}

	return bk, nil
}

func (b *Broker) subscribe(module string, handler any) error {
	if b.client == nil {
		return nil
	}

	callback, ok := handler.(func(mqtt.Client, mqtt.Message))
	if !ok {
		return errors.New("handler is not a func(mqtt.Client, mqtt.Message) function")
	}

	if !b.client.IsConnected() {
		if token := b.client.Connect(); token.Wait() && token.Error() != nil {
			return fmt.Errorf("connect to emqx failed with %w", token.Error())
		}
	}

	token := b.client.Subscribe(module, 1, callback)
	token.Wait()
	if token.Error() != nil {
		return fmt.Errorf("subscribe module %s failed with %w", module, token.Error())
	}
	b.logger.Info("Subscribed to topic", zap.String("topic", module))
	return nil
}

func (b *Broker) Run() {
	g := errgroup.Group{}

	// receive TaskRequest from server
	g.Go(func() error {
		var alwaysRetry func(err error) bool
		alwaysRetry = func(err error) bool { return true }
		for {
			select {
			case <-b.ctx.Done():
				b.logger.Info("exit the broker write goroutine [receive TaskRequest]")
				return nil
			case msg, ok := <-b.receiveCh:
				if !ok {
					b.logger.Error("wrong TaskRequest message")
					continue
				}
				err := retry.OnError(retry.DefaultBackoff, alwaysRetry, func() error {
					err := b.chatRequestHandler(msg)
					if err != nil {
						return err
					}
					return nil
				})
				if err != nil {
					b.logger.Error(err.Error())
				}
			}
		}
	})

	// send chatReply status to server
	g.Go(func() error {
		var alwaysRetry func(err error) bool
		alwaysRetry = func(err error) bool { return true }
		for {
			select {
			case <-b.ctx.Done():
				b.logger.Info("exit the broker write goroutine [send chatReply]")
				return nil
			case msg, ok := <-b.sendCh:
				if !ok {
					b.logger.Error("wrong chatReply message")
					continue
				}
				err := retry.OnError(retry.DefaultBackoff, alwaysRetry, func() error {
					err := b.publishChatReply(constant.TopicChatTaskReply, &msg.Payload, msg.IsQuit)
					if err != nil {
						return fmt.Errorf("send chatRequest failed with %s", err)
					}
					return nil
				})
				if err != nil {
					b.logger.Error(err.Error())
					event.EmitEvent(constant.EventTaskChatHandle, task.StatusFinished)
				}
			}
		}
	})

	// receive DeviceSettingsRequest from server
	g.Go(func() error {
		var alwaysRetry func(err error) bool
		alwaysRetry = func(err error) bool { return true }
		for {
			select {
			case <-b.ctx.Done():
				b.logger.Info("exit the broker write goroutine [receive DeviceSettingsRequest]")
				return nil
			case msg, ok := <-b.receiveChSetting:
				if !ok {
					b.logger.Error("wrong DeviceSettingsRequest message")
					continue
				}
				err := retry.OnError(retry.DefaultBackoff, alwaysRetry, func() error {
					err := b.settingRequestHandler(msg)
					if err != nil {
						return err
					}
					return nil
				})
				if err != nil {
					b.logger.Error(err.Error())
				}
			}
		}
	})

	err := g.Wait()
	if err != nil {
		panic(err)
	}
}

func (b *Broker) Relink() error {
	if service.UserServiceImpl == nil {
		return fmt.Errorf("user service is nil")
	}

	// close the old link
	b.Close()

	uid := service.UserServiceImpl.GetUserID()
	if len(uid) == 0 {
		// terminate the MQTT connection upon user logout
		b.client = nil
		return nil
	}

	connectID := service.UserServiceImpl.GetDeviceID()
	opts := mqtt.NewClientOptions().AddBroker(b.cfg.Addr).
		SetClientID(connectID).
		SetUsername(b.cfg.Username).
		SetPassword(b.cfg.Password).
		SetDefaultPublishHandler(b.cfg.MessageHandler).
		SetOnConnectHandler(b.onConnectHandler).
		SetConnectionLostHandler(b.connectionLostHandler).
		SetAutoReconnect(true).
		SetKeepAlive(30 * time.Second)
	if b.cfg.TLSConfig != nil && b.cfg.TLSConfig.TLS != nil {
		opts.SetTLSConfig(b.cfg.TLSConfig.TLS)
	}
	_client := mqtt.NewClient(opts)
	tk := _client.Connect()
	if tk.Wait() && tk.Error() != nil {
		return tk.Error()
	}

	b.client = _client

	// subscribe chat task topic from server
	topic := service.UserServiceImpl.GetBrokerDeviceTopic()
	err := b.subscribe(topic, b.receiveCloudRequestHandler)
	if err != nil {
		return fmt.Errorf("subscribe chat task topic error: %w, topic: %s", err, topic)
	}

	return nil
}

func (b *Broker) Close() {
	if b.client == nil {
		return
	}
	// 取消订阅
	topic := service.UserServiceImpl.GetBrokerDeviceTopic()
	if token := b.client.Unsubscribe(topic); token.Wait() && token.Error() != nil {
		b.logger.Error("Failed to unsubscribe from topic", zap.Error(token.Error()), zap.String("topic", topic))
	}

	// 断开连接
	b.client.Disconnect(250)
	b.logger.Info("exit emqx broker successfully")
}

func (b *Broker) connectionLostHandler(client mqtt.Client, err error) {
	b.logger.Warn("broker connect lost", zap.Error(err))
	err = b.reconnect(client)
	// 尝试重新连接
	for err != nil || !client.IsConnected() {
		//b.logger.Debug("Reconnecting...")
		err = b.reconnect(client)
		if err != nil {
			//b.logger.Debug("Reconnect failed. Retrying in 5 seconds...", zap.Error(err))
			time.Sleep(5 * time.Second)
		} else {
			b.logger.Debug("Reconnected successfully.")
			break
		}
	}
}

func (b *Broker) onConnectHandler(client mqtt.Client) {
	b.logger.Info("Connected to emqx broker successfully")
	// subscribe chat task topic from server
	topic := service.UserServiceImpl.GetBrokerDeviceTopic()
	err := b.subscribe(topic, b.receiveCloudRequestHandler)
	if err != nil {
		b.logger.Error("Failed to subscribe to topic", zap.Error(err), zap.String("topic", topic))
	}

	err = b.connectRequestHandler()
	if err != nil {
		b.logger.Error("failed to do the connectRequestHandler", zap.Error(err))
	}
}

func (b *Broker) reconnect(client mqtt.Client) error {
	token := client.Connect()
	if token.Wait() && token.Error() != nil {
		return token.Error()
	}

	// subscribe chat task topic from server
	topic := service.UserServiceImpl.GetBrokerDeviceTopic()
	err := b.subscribe(topic, b.receiveCloudRequestHandler)
	if err != nil {
		return fmt.Errorf("subscribe chat task topic error: %w, topic: %s", err, topic)
	}

	err = b.connectRequestHandler()
	if err != nil {
		return fmt.Errorf("failed to do the connectRequestHandler error: %w", err)
	}

	return nil
}
