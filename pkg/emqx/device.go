// Package emqx -----------------------------
// @file      : device.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 15:39
// -------------------------------------------
package emqx

import (
	"AgenticAI-Client/internal/constant"
	"encoding/json"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	"go.uber.org/zap"
)

func (b *Broker) PublishDeviceBindingReply(reply *emqx.DeviceResponse) error {
	if b.client == nil || reply == nil {
		return nil
	}

	data, err := json.Marshal(reply)
	if err != nil {
		b.logger.Error("Failed to marshal emqx.DeviceResponse message", zap.Any("reply", *reply), zap.Error(err))
		return err
	}
	token := b.client.Publish(constant.TopicDeviceBindingReply, 1, false, data)
	token.Wait()
	if token.Error() != nil {
		b.logger.Error("MQTT publish failed", zap.Any("reply", *reply), zap.Error(token.Error()))
		return token.Error()
	}
	b.logger.Debug("send MQTT message", zap.String("code", reply.Code), zap.String("name", reply.Name), zap.Any("status", reply.Status), zap.String("type", reply.Type), zap.String("ip", reply.IP),
		zap.Bool("accessibilityPermission", reply.AccessibilityPermission), zap.Bool("screenRecordPermission", reply.ScreenRecordPermission))

	return nil
}

func (b *Broker) PublishDeviceHeartbeatReply(reply *emqx.DeviceHeartbeatResponse) error {
	if b.client == nil || reply == nil {
		return nil
	}

	data, err := json.Marshal(reply)
	if err != nil {
		b.logger.Error("Failed to marshal emqx.DeviceHeartbeatResponse message", zap.Any("reply", *reply), zap.Error(err))
		return err
	}
	token := b.client.Publish(constant.TopicDeviceHeartbeatReply, 1, false, data)
	token.Wait()
	if token.Error() != nil {
		//b.logger.Error("MQTT publish failed", zap.Any("reply", *reply), zap.Error(token.Error()))
		return token.Error()
	}

	return nil
}

func (b *Broker) RegisterSettingRequestHandler(handler func(request *emqx.DeviceSettingsRequest) error) {
	b.settingRequestHandler = handler
}

func (b *Broker) RegisterQuitAppHandler(handler func() error) {
	b.quitAppHandler = handler
}
