package oss

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"go.uber.org/zap"
)

// UploadResult represents the result of an upload operation
type UploadResult struct {
	URL       string `json:"url"`
	ObjectKey string `json:"objectKey"`
	Size      int64  `json:"size"`
	Filename  string `json:"filename"`
}

// UploadFile uploads a file to OSS
func (c *Client) UploadFile(ctx context.Context, filePath string, objectKey string) (*UploadResult, error) {
	// If objectKey is empty, generate one based on the filename
	if objectKey == "" {
		objectKey = GenerateObjectKey(filepath.Base(filePath))
	}

	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		c.logger.Error("Failed to get file info", zap.String("filePath", filePath), zap.Error(err))
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Upload file
	err = c.bucket.PutObjectFromFile(objectKey, filePath)
	if err != nil {
		c.logger.Error("Failed to upload file", zap.String("filePath", filePath), zap.Error(err))
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Generate URL
	url, err := c.GenerateURL(objectKey)
	if err != nil {
		return nil, err
	}

	return &UploadResult{
		URL:       url,
		ObjectKey: objectKey,
		Size:      fileInfo.Size(),
		Filename:  filepath.Base(filePath),
	}, nil
}

// UploadBytes uploads bytes to OSS
func (c *Client) UploadBytes(ctx context.Context, data []byte, objectKey string, filename string) (*UploadResult, error) {
	// If objectKey is empty, generate one based on the filename
	if objectKey == "" {
		objectKey = GenerateObjectKey(filename)
	}

	// Upload bytes
	err := c.bucket.PutObject(objectKey, bytes.NewReader(data))
	if err != nil {
		c.logger.Error("Failed to upload bytes", zap.String("objectKey", objectKey), zap.Error(err))
		return nil, fmt.Errorf("failed to upload bytes: %w", err)
	}

	// Generate URL
	url, err := c.GenerateURL(objectKey)
	if err != nil {
		return nil, err
	}

	return &UploadResult{
		URL:       url,
		ObjectKey: objectKey,
		Size:      int64(len(data)),
		Filename:  filename,
	}, nil
}

// UploadMultipartFile uploads a multipart file to OSS
func (c *Client) UploadMultipartFile(ctx context.Context, file *multipart.FileHeader, objectKey string) (*UploadResult, error) {
	// If objectKey is empty, generate one based on the filename
	if objectKey == "" {
		objectKey = GenerateObjectKey(file.Filename)
	}

	// Open file
	src, err := file.Open()
	if err != nil {
		c.logger.Error("Failed to open multipart file", zap.String("filename", file.Filename), zap.Error(err))
		return nil, fmt.Errorf("failed to open multipart file: %w", err)
	}
	defer src.Close()

	// Read file content
	data, err := io.ReadAll(src)
	if err != nil {
		c.logger.Error("Failed to read multipart file", zap.String("filename", file.Filename), zap.Error(err))
		return nil, fmt.Errorf("failed to read multipart file: %w", err)
	}

	// Upload bytes
	return c.UploadBytes(ctx, data, objectKey, file.Filename)
}

// UploadImage uploads an image to OSS
// This is a convenience method that checks if the file is an image before uploading
func (c *Client) UploadImage(ctx context.Context, filePath string, objectKey string) (*UploadResult, error) {
	// Check if file is an image
	if !isImageFile(filePath) {
		return nil, fmt.Errorf("file is not an image: %s", filePath)
	}

	// Upload file
	return c.UploadFile(ctx, filePath, objectKey)
}

// UploadImageBytes uploads image bytes to OSS
// This is a convenience method that checks if the data is an image before uploading
func (c *Client) UploadImageBytes(ctx context.Context, data []byte, objectKey string, filename string) (*UploadResult, error) {
	// Check if data is an image
	if !isImageData(data) {
		return nil, fmt.Errorf("data is not an image")
	}

	// Upload bytes
	return c.UploadBytes(ctx, data, objectKey, filename)
}

// UploadImageMultipartFile uploads a multipart image file to OSS
// This is a convenience method that checks if the file is an image before uploading
func (c *Client) UploadImageMultipartFile(ctx context.Context, file *multipart.FileHeader, objectKey string) (*UploadResult, error) {
	// Check if file is an image
	if !isImageFilename(file.Filename) {
		return nil, fmt.Errorf("file is not an image: %s", file.Filename)
	}

	// Upload multipart file
	return c.UploadMultipartFile(ctx, file, objectKey)
}

// isImageFile checks if a file is an image based on its extension and content type
func isImageFile(filePath string) bool {
	// Check extension
	ext := strings.ToLower(filepath.Ext(filePath))
	if !isImageExtension(ext) {
		return false
	}

	// Open file
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	// Read first 512 bytes to detect content type
	buffer := make([]byte, 512)
	_, err = file.Read(buffer)
	if err != nil {
		return false
	}

	// Detect content type
	contentType := http.DetectContentType(buffer)
	return strings.HasPrefix(contentType, "image/")
}

// isImageData checks if data is an image based on its content type
func isImageData(data []byte) bool {
	// Detect content type
	contentType := http.DetectContentType(data)
	return strings.HasPrefix(contentType, "image/")
}

// isImageFilename checks if a filename has an image extension
func isImageFilename(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return isImageExtension(ext)
}

// isImageExtension checks if an extension is an image extension
func isImageExtension(ext string) bool {
	imageExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".tiff", ".svg"}
	for _, e := range imageExtensions {
		if ext == e {
			return true
		}
	}
	return false
}
