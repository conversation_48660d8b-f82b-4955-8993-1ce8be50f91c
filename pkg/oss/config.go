package oss

// Config represents the configuration for Alibaba Cloud OSS
type Config struct {
	// Endpoint is the OSS endpoint, e.g., "oss-cn-hangzhou.aliyuncs.com"
	Endpoint string `yaml:"endpoint" mapstructure:"endpoint"`
	// AccessKeyID is the Alibaba Cloud access key ID
	AccessKeyID string `yaml:"accessKeyID" mapstructure:"accessKeyID"`
	// AccessKeySecret is the Alibaba Cloud access key secret
	AccessKeySecret string `yaml:"accessKeySecret" mapstructure:"accessKeySecret"`
	// BucketName is the OSS bucket name
	BucketName string `yaml:"bucketName" mapstructure:"bucketName"`
	// URLPrefix is the prefix for the returned URL (optional)
	URLPrefix string `yaml:"urlPrefix" mapstructure:"urlPrefix"`
	// Timeout is the timeout for OSS operations in seconds (default: 60)
	Timeout int `yaml:"timeout" mapstructure:"timeout"`
	// IsPrivate indicates whether the bucket is private (default: false)
	IsPrivate bool `yaml:"isPrivate" mapstructure:"isPrivate"`
	// ExpireTime is the expiration time for signed URLs in seconds (default: 3600)
	ExpireTime int64 `yaml:"expireTime" mapstructure:"expireTime"`
}

// Complete sets default values for the configuration
func (cfg *Config) Complete() {
	if cfg.Timeout <= 0 {
		cfg.Timeout = 60
	}
	if cfg.ExpireTime <= 0 {
		cfg.ExpireTime = 3600
	}
}
