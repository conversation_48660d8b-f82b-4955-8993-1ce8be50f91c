package oss

import (
	"context"
	"os"
	"testing"

	"github.com/spf13/viper"
)

func TestExample(t *testing.T) {

	// 设置配置文件
	// viper.AddConfigPath(wd)
	viper.AddConfigPath("/Users/<USER>/code/AgenticAI-Client") // 也在上一级目录查找
	viper.SetConfigType("yaml")
	viper.SetConfigName("config")

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		t.Fatalf("Failed to read config file: %v", err)
	}

	// 获取 OSS 配置
	var config Config
	if err := viper.UnmarshalKey("oss", &config); err != nil {
		t.Fatalf("Failed to unmarshal OSS config: %v", err)
	}

	// 创建测试用的临时文件
	tempFile, err := os.CreateTemp("", "test-*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	// 写入一些测试数据
	testData := []byte("Hello, OSS!")
	if _, err := tempFile.Write(testData); err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tempFile.Close()

	// 创建 OSS 客户端
	client, err := NewClient(&config)
	if err != nil {
		t.Fatalf("Failed to create OSS client: %v", err)
	}

	// 测试文件上传
	result, err := client.UploadFile(context.Background(), tempFile.Name(), "test/example.txt")
	if err != nil {
		t.Fatalf("Failed to upload file: %v", err)
	}
	if result.URL == "" {
		t.Error("Expected URL to be non-empty")
	}

	// // 测试文件下载
	// downloadedData, err := client.DownloadObject(context.Background(), result.ObjectKey)
	// if err != nil {
	// 	t.Fatalf("Failed to download object: %v", err)
	// }
	// if string(downloadedData) != string(testData) {
	// 	t.Errorf("Downloaded data doesn't match original. Got %s, want %s", downloadedData, testData)
	// }

	// // 测试文件删除
	// if err := client.DeleteObject(result.ObjectKey); err != nil {
	// 	t.Fatalf("Failed to delete object: %v", err)
	// }

	// // 验证文件已被删除
	// _, err = client.DownloadObject(context.Background(), result.ObjectKey)
	// if err == nil {
	// 	t.Error("Expected error when downloading deleted object")
	// }
}
