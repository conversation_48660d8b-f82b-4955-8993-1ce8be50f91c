package oss

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"go.uber.org/zap"
)

// Client represents an OSS client
type Client struct {
	client     *oss.Client
	bucket     *oss.Bucket
	config     *Config
	logger     *zap.Logger
	httpClient *http.Client
}

// NewClient creates a new OSS client
func NewClient(cfg *Config) (*Client, error) {
	logger := zap.L().With(zap.String("module", "oss"))

	// Create OSS client
	client, err := oss.New(cfg.Endpoint, cfg.AccessKeyID, cfg.AccessKeySecret)
	if err != nil {
		logger.Error("Failed to create OSS client", zap.Error(err))
		return nil, fmt.Errorf("failed to create OSS client: %w", err)
	}

	// Get bucket
	bucket, err := client.Bucket(cfg.BucketName)
	if err != nil {
		logger.Error("Failed to get bucket", zap.Error(err))
		return nil, fmt.Errorf("failed to get bucket: %w", err)
	}

	// Create HTTP client with timeout
	httpClient := &http.Client{
		Timeout: time.Duration(cfg.Timeout) * time.Second,
	}

	return &Client{
		client:     client,
		bucket:     bucket,
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
	}, nil
}

// GetBucket returns the OSS bucket
func (c *Client) GetBucket() *oss.Bucket {
	return c.bucket
}

// GetClient returns the OSS client
func (c *Client) GetClient() *oss.Client {
	return c.client
}

// GetConfig returns the OSS configuration
func (c *Client) GetConfig() *Config {
	return c.config
}

// GenerateURL generates a URL for the given object key
func (c *Client) GenerateURL(objectKey string) (string, error) {
	if c.config.IsPrivate {
		// Generate signed URL for private bucket
		signedURL, err := c.bucket.SignURL(objectKey, oss.HTTPGet, c.config.ExpireTime)
		if err != nil {
			c.logger.Error("Failed to generate signed URL", zap.Error(err))
			return "", fmt.Errorf("failed to generate signed URL: %w", err)
		}
		return signedURL, nil
	}

	// For public bucket, just return the URL
	if c.config.URLPrefix != "" {
		return fmt.Sprintf("%s/%s", strings.TrimRight(c.config.URLPrefix, "/"), objectKey), nil
	}

	return fmt.Sprintf("https://%s.%s/%s", c.config.BucketName, c.config.Endpoint, objectKey), nil
}

// IsObjectExist checks if an object exists
func (c *Client) IsObjectExist(objectKey string) (bool, error) {
	exist, err := c.bucket.IsObjectExist(objectKey)
	if err != nil {
		c.logger.Error("Failed to check if object exists", zap.String("objectKey", objectKey), zap.Error(err))
		return false, fmt.Errorf("failed to check if object exists: %w", err)
	}
	return exist, nil
}

// DeleteObject deletes an object
func (c *Client) DeleteObject(objectKey string) error {
	err := c.bucket.DeleteObject(objectKey)
	if err != nil {
		c.logger.Error("Failed to delete object", zap.String("objectKey", objectKey), zap.Error(err))
		return fmt.Errorf("failed to delete object: %w", err)
	}
	return nil
}

// DownloadObject downloads an object
func (c *Client) DownloadObject(ctx context.Context, objectKey string) ([]byte, error) {
	// Get object
	body, err := c.bucket.GetObject(objectKey)
	if err != nil {
		c.logger.Error("Failed to get object", zap.String("objectKey", objectKey), zap.Error(err))
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	defer body.Close()

	// Read object content
	data, err := io.ReadAll(body)
	if err != nil {
		c.logger.Error("Failed to read object content", zap.String("objectKey", objectKey), zap.Error(err))
		return nil, fmt.Errorf("failed to read object content: %w", err)
	}

	return data, nil
}

// GenerateObjectKey generates a unique object key for the given filename
func GenerateObjectKey(filename string) string {
	// Get file extension
	ext := filepath.Ext(filename)

	// Generate timestamp
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)

	// Generate object key: timestamp + original extension
	return fmt.Sprintf("%d%s", timestamp, ext)
}
