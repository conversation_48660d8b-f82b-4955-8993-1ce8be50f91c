package log

import (
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
	"time"
)

var globalLevel = zap.NewAtomicLevel()

func Init(console bool, cfg *Config) {
	setLogLevel(cfg.Level)
	if console {
		initStdLogger()
	} else {
		initFileLogger(cfg)
	}
}

func setLogLevel(level int8) {
	l := zapcore.Level(level)
	old := globalLevel.Level()
	globalLevel.SetLevel(l)
	zap.S().Infof("switch level from %s to %s", old, l)
}

func initStdLogger() {
	cfg := zap.NewProductionConfig()
	cfg.Level = globalLevel
	cfg.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout(time.DateTime)
	logger, err := cfg.Build()
	if err != nil {
		panic(err)
	}
	zap.ReplaceGlobals(logger)
}

func initFileLogger(cfg *Config) {
	logger := newFileLogger(cfg)

	// set to global
	zap.ReplaceGlobals(logger)
}

func newFileLogger(cfg *Config) *zap.Logger {
	var logger *zap.Logger

	// rotate writer
	w := zapcore.AddSync(&lumberjack.Logger{
		Filename:   cfg.Path,
		MaxSize:    cfg.MaxSizeMB,
		MaxBackups: cfg.Backup,
		MaxAge:     cfg.MaxAgeDay,
		Compress:   true,
	})

	// core
	zcfg := zap.NewProductionEncoderConfig()
	zcfg.TimeKey = "T"
	zcfg.LevelKey = "L"
	zcfg.NameKey = "N"
	zcfg.CallerKey = "C"
	zcfg.MessageKey = "M"
	zcfg.EncodeTime = zapcore.TimeEncoderOfLayout(time.DateTime)
	core := zapcore.NewCore(zapcore.NewJSONEncoder(zcfg), w, globalLevel)
	logger = zap.New(core, zap.Fields(zap.Int("P", os.Getpid())), zap.AddCaller())

	return logger
}
