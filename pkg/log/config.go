package log

import (
	"AgenticAI-Client/internal/constant"
	"path/filepath"
)

type Config struct {
	Path      string `yaml:"path" mapstructure:"path"`
	MaxSizeMB int    `yaml:"maxSizeMegabytes" mapstructure:"maxSizeMegabytes"`
	MaxAgeDay int    `yaml:"maxAgeDay" mapstructure:"maxAgeDay"`
	Backup    int    `yaml:"backupCount" mapstructure:"backupCount"`
	Level     int8   `yaml:"level" mapstructure:"level"`
}

func (cfg *Config) Complete() {
	if cfg.Path == "" {
		cfg.Path = filepath.Join("./", constant.APPName+".log")
	}
	if cfg.MaxAgeDay == 0 {
		cfg.MaxAgeDay = 7
	}
	if cfg.MaxSizeMB == 0 {
		cfg.MaxSizeMB = 100
	}
	if cfg.Backup == 0 {
		cfg.Backup = 7
	}
}
