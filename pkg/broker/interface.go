// Package broker -----------------------------
// @file      : interface.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 13:35
// -------------------------------------------
package broker

import (
	"AgenticAI-Client/pkg/workflow/message"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
)

type Interface interface {
	Run()
	Close()
	PublishChatReply(reply *message.TaskResponse)
	RegisterChatRequestHandler(handler func(request *message.TaskRequest) error)
	Relink() error
	RegisterConnectHandler(handler func() error)

	// for device
	PublishDeviceBindingReply(reply *emqx.DeviceResponse) error
	PublishDeviceHeartbeatReply(reply *emqx.DeviceHeartbeatResponse) error
	RegisterSettingRequestHandler(handler func(request *emqx.DeviceSettingsRequest) error)
	RegisterQ<PERSON><PERSON><PERSON>Hand<PERSON>(handler func() error)
}
