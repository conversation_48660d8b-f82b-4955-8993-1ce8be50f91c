// Package broker -----------------------------
// @file      : mock_broker.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/14 13:37
// -------------------------------------------
package broker

import (
	"AgenticAI-Client/pkg/workflow/message"
	"context"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	"go.uber.org/zap"
)

type MockImpl struct {
	ctx  context.Context
	stop chan interface{}
}

func NewMockImpl(ctx context.Context) Interface {
	impl := &MockImpl{
		ctx:  ctx,
		stop: make(chan interface{}),
	}
	return impl
}

func (m *MockImpl) Subscribe(module string, handler any) error {
	zap.L().Info("Subscribe")
	return nil
}

func (m *MockImpl) UnSubscribe(module string) error {
	zap.L().Info("UnSubscribe")
	return nil
}

func (m *MockImpl) Run() {
	zap.L().Info("Run MockImpl broker")
	for {
		select {
		case <-m.ctx.Done():
			zap.L().Info("exit MockImpl broker")
			return
		case <-m.stop:
			zap.L().Info("close MockImpl broker")
			return
		}
	}
}

func (m *MockImpl) Close() {
	zap.L().Info("Close MockImpl broker")
	close(m.stop)
}

func (m *MockImpl) PublishChatReply(reply *message.TaskResponse) {
	zap.L().Info("PublishChatReply MockImpl broker")
}

func (m *MockImpl) RegisterChatRequestHandler(handler func(request *message.TaskRequest) error) {
	zap.L().Info("RegisterChatRequestHandler MockImpl broker")
}

func (m *MockImpl) Relink() error {
	//TODO implement me
	panic("implement me")
}

func (m *MockImpl) RegisterConnectHandler(handler func() error) {
	panic("implement me")
}

func (m *MockImpl) PublishDeviceBindingReply(reply *emqx.DeviceResponse) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockImpl) PublishDeviceHeartbeatReply(reply *emqx.DeviceHeartbeatResponse) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockImpl) RegisterSettingRequestHandler(handler func(request *emqx.DeviceSettingsRequest) error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockImpl) RegisterQuitAppHandler(handler func() error) {
	//TODO implement me
	panic("implement me")
}
