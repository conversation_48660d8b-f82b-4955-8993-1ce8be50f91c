// Package cache -----------------------------
// @file      : cache.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/8 09:55
// -------------------------------------------
package cache

import (
	"AgenticAI-Client/pkg/oss"
	"AgenticAI-Client/pkg/service"
	pb "AgenticAI-Client/pkg/workflow/gen"
	"AgenticAI-Client/pkg/workflow/message"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	"go.uber.org/zap"
)

type TaskCache struct {
	TReq    []emqx.TaskRequest        `json:"t_req"`
	TRsp    emqx.TaskResponse         `json:"t_rsp"`
	StepMap map[string]*emqx.TaskStep `json:"step_map"`
	Path    string                    `json:"path"`
}

func NewTaskCache(dataDir string) (*TaskCache, error) {
	taskCachePath := filepath.Join(dataDir, "task_cache.json")

	_, err := os.Stat(taskCachePath)
	if os.IsNotExist(err) {
		_taskCache := TaskCache{
			TReq:    []emqx.TaskRequest{},
			TRsp:    emqx.TaskResponse{},
			StepMap: map[string]*emqx.TaskStep{},
			Path:    taskCachePath,
		}

		var data []byte
		data, err = json.Marshal(&_taskCache)
		if err != nil {
			return nil, fmt.Errorf("无法生成任务缓存信息: %w", err)
		}

		err = os.WriteFile(taskCachePath, data, 0600)
		if err != nil {
			return nil, fmt.Errorf("无法写入任务缓存信息文件: %w", err)
		}

		return &_taskCache, nil
	}

	// 文件存在，读取任务缓存信息
	data, err := os.ReadFile(taskCachePath)
	if err != nil {
		return nil, fmt.Errorf("无法读取任务缓存信息文件: %w", err)
	}

	var _taskCache TaskCache
	err = json.Unmarshal(data, &_taskCache)
	if err != nil {
		return nil, fmt.Errorf("无法解析任务缓存信息: %w", err)
	}

	return &_taskCache, nil
}

func (c *TaskCache) Update() error {
	if c == nil {
		return nil
	}

	data, err := json.Marshal(c)
	if err != nil {
		return fmt.Errorf("无法更新任务缓存信息: %w", err)
	}

	err = os.WriteFile(c.Path, data, 0600)
	if err != nil {
		return fmt.Errorf("无法写入任务缓存信息文件: %w", err)
	}

	return nil
}

func (c *TaskCache) Reset() error {
	if c == nil {
		return nil
	}

	c.TReq = []emqx.TaskRequest{}
	c.TRsp = emqx.TaskResponse{}
	c.StepMap = map[string]*emqx.TaskStep{}

	return c.Update()
}

func (c *TaskCache) Clear() error {
	if c == nil {
		return nil
	}

	info, err := os.Stat(c.Path)
	if err != nil {
		return err
	}
	if info.IsDir() {
		return fmt.Errorf("路径是目录，请改用RemoveAll")
	}
	if info.Mode().Perm()&0200 == 0 { // 检查写权限
		return fmt.Errorf("文件无写权限")
	}

	err = os.Remove(c.Path)
	if err != nil {
		return err
	}

	return nil
}

func (c *TaskCache) ParseAndReadChatReply(ctx context.Context, reply *pb.ChatReply, oc *oss.Client) {
	if reply == nil {
		return
	}

	_subType := reply.GetMsgType()
	_type := message.GetMsgType(_subType)
	switch _type {
	case message.MsgTypeTextResponse:
		if _subType == message.SubMsgTypeNotify {
			if !reply.IsEnd {
				c.TRsp.Status = emqx.RunningStatus
				c.TRsp.DeviceId = service.UserServiceImpl.GetDeviceID()
				c.TRsp.TaskId = reply.GetConversationId()
				c.TRsp.ExecutedAt = time.Now().Unix()
			} else {
				c.TRsp.Status = emqx.SuccessStatus
				c.TRsp.IsEnd = true
			}
			return
		}
		if _subType == message.SubMsgTypePlan {
			textRes := reply.GetTextResponse()
			if textRes != nil {
				c.TRsp.Plan = reply.GetTextResponse().Content
			}
			return
		}

		// 总 task result
		if _subType == message.SubMsgTypeTaskResult {
			c.TRsp.Result = reply.GetTextResponse().Content
			return
		}
		// TODO: maybe should handle permission subMsgType

	case message.MsgTypeActionResponse:
		actRes := reply.GetActionResponse()
		if actRes != nil {
			// upload to oss
			photo := actRes.GetImageDir()
			if len(photo) > 0 {
				res, err := oc.UploadFile(ctx, photo, fmt.Sprintf("ai/%s", extractPathPart(photo)))
				if err != nil {
					zap.L().Error("upload file failed", zap.Error(err))
				} else {
					photo = res.URL
				}
			}

			id, action := actRes.GetStepId(), actRes.GetAction()
			acList := strings.Split(action, "&")
			step := emqx.TaskStep{
				Id:            id,
				CurrentAction: acList[len(acList)-1],
				Actions:       acList,
				Photo:         photo,
				Result:        actRes.GetResult(),
				Goal:          actRes.GetGoal(),
				Status:        actRes.GetState(),
			}
			c.StepMap[id] = &step

			var slice []*emqx.TaskStep
			for _, v := range c.StepMap {
				slice = append(slice, v)
			}
			sort.Slice(slice, func(i, j int) bool {
				left, _ := strconv.Atoi(slice[i].Id)
				right, _ := strconv.Atoi(slice[j].Id)

				return left < right
			})
			c.TRsp.Steps = slice
		}
	case message.MsgTypeErrorResponse:
		errRes := reply.GetErrorResponse()
		if errRes != nil {
			//c.TRsp.Status = emqx.FailedStatus
			step, ok := c.StepMap[errRes.GetStepId()]
			if !ok {
				step = &emqx.TaskStep{
					Id:     errRes.GetStepId(),
					Result: errRes.GetContent(),
				}
			}
			step.Result = errRes.GetContent()
			step.Status = emqx.FailedStatus
			c.StepMap[step.Id] = step

			var slice []*emqx.TaskStep
			for _, v := range c.StepMap {
				slice = append(slice, v)
			}
			sort.Slice(slice, func(i, j int) bool {
				left, _ := strconv.Atoi(slice[i].Id)
				right, _ := strconv.Atoi(slice[j].Id)

				return left < right
			})
			c.TRsp.Steps = slice
		}
	default:
		zap.L().Error("unknown msg type", zap.Uint8("type", uint8(_type)), zap.String("subType", _subType))
	}

	err := c.Update()
	if err != nil {
		zap.L().Error("update task cache failed", zap.Error(err))
	}
}

// extractPathPart extract path part
// input: /var/folders/rq/x22g5tc53rs4bmjn_c74f4580000gn/T/temp_files/681ca2fb596b991793bb1fea/screenshot_to_use_4.png
// output: 681ca2fb596b991793bb1fea/screenshot_to_use_4.png
func extractPathPart(fullPath string) string {
	// 获取文件名
	fileName := filepath.Base(fullPath)
	// 获取上一级目录名
	dirName := filepath.Base(filepath.Dir(fullPath))
	return filepath.Join(dirName, fileName)
}
