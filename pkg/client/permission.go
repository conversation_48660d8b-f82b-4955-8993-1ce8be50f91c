// Package client -----------------------------
// @file      : permission.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/23 14:38
// -------------------------------------------
package client

import (
	"AgenticAI-Client/internal/event"
	pb "AgenticAI-Client/pkg/workflow/gen"
	"AgenticAI-Client/pkg/workflow/message"
	"context"
	"fmt"
	"github.com/wailsapp/wails/v3/pkg/application"
	"go.uber.org/zap"
	"io"
)

func (c *AIClient) PermissionStream(ctx context.Context, req *pb.ChatRequest) error {
	// 建立流
	streamCtx, cancel := context.WithCancel(ctx)
	stream, err := c.chatSrvClient.Chat(streamCtx)
	if err != nil {
		cancel()
		return fmt.Errorf("start permisstion stream failed with %w", err)
	}
	defer cancel()

	//req := message.PermissionRequest(message.DevicePermissionValueRequest)
	c.logger.Debug("send permission request to agent", zap.Any("request", req))

	err = stream.Send(req)
	if err != nil {
		if err == io.EOF {
			return nil
		}
		return fmt.Errorf("stream.Send failed failed with %w", err)
	}

	chunk, err := stream.Recv()
	if err != nil {
		if err == io.EOF {
			return nil
		}
		return fmt.Errorf("stream.Receive failed with %w", err)
	}
	zap.L().Debug("Received chunk", zap.Any("chunk", chunk))

	err = stream.CloseSend()
	if err != nil {
		return fmt.Errorf("stream.CloseSend failed with %w", err)
	}

	return nil
}

func (c *AIClient) HandlePermissionRequest(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	val, ok := vals[0].(event.PermissionStatus)
	if !ok {
		return
	}

	var permissionType string
	switch val {
	case event.PermissionCheck:
		permissionType = message.DevicePermissionValueCheck
	case event.PermissionRequest:
		permissionType = message.DevicePermissionValueRequest
	case event.PermissionToAccess:
		permissionType = message.DevicePermissionValueToAccess
	case event.PermissionToScreen:
		permissionType = message.DevicePermissionValueToScreen
	default:
		return
	}

	req := message.PermissionRequest(permissionType)
	err := c.PermissionStream(c.ctx, req)
	if err != nil {
		zap.L().Error("PermissionStream failed", zap.Error(err))
	}
}
