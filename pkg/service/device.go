// Package service -----------------------------
// @file      : device.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/27 13:35
// -------------------------------------------
package service

import (
	"AgenticAI-Client/internal/constant"
	"fmt"
	"github.com/blackfireio/osinfo"
	"github.com/brunogui0812/sysprofiler"
	"github.com/denisbrodbeck/machineid"
	externalip "github.com/glendc/go-external-ip"
	"github.com/shirou/gopsutil/v4/host"
	"go.uber.org/zap"
	"os/exec"
	"strings"
)

type DeviceInfo struct {
	ID   string  `json:"id"`
	IP   string  `json:"ip"`
	Name string  `json:"name"`
	OS   *OSInfo `json:"os"`
}

type OSInfo struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Family       string `json:"family"`
	Architecture string `json:"architecture"`
	Version      string `json:"version"`
	Build        string `json:"build"`
}

func NewDevice() (*DeviceInfo, error) {
	id, err := machineid.ProtectedID(constant.APPName)
	if err != nil {
		return nil, err
	}

	os, err := osinfo.GetOSInfo()
	if err != nil {
		return nil, err
	}
	_oi := &OSInfo{
		ID:           os.ID,
		Name:         os.Name,
		Family:       os.Family,
		Architecture: os.Architecture,
		Version:      os.Version,
		Build:        os.Build,
	}

	consensus := externalip.DefaultConsensus(nil, nil)
	ip, err := consensus.ExternalIP()
	if err != nil {
		return nil, err
	}

	name, err := GetDeviceName()
	if err != nil {
		zap.L().Warn("get device name failed", zap.Error(err))
	}
	if len(name) == 0 {
		name = _oi.Name
	}

	di := &DeviceInfo{
		ID:   id,
		OS:   _oi,
		IP:   ip.String(),
		Name: name,
	}
	return di, nil
}

// GetDeviceName 获取设备名称
func GetDeviceName() (string, error) {
	computerName, err := getComputerName()
	if err != nil {
		return "", err
	}

	serialNum, err := getSerialNum()
	if err != nil {
		return "", err
	}

	if len(computerName) > 0 && len(serialNum) > 4 {
		return fmt.Sprintf("%s-%s", computerName, serialNum[:4]), nil
	}
	return computerName, nil
}

// 获取计算机名称
func getComputerName() (string, error) {
	cmd := exec.Command("scutil", "--get", "ComputerName")
	nameBytes, _ := cmd.Output()
	computerName := strings.TrimSpace(string(nameBytes))
	if len(computerName) > 0 {
		return computerName, nil
	}

	h, err := host.Info()
	if err != nil {
		return "", err
	}
	return h.Hostname, nil
}

// getSerialNum 获取序列号
func getSerialNum() (string, error) {
	hardwareInfo, err := sysprofiler.Hardware()
	if err != nil {
		return "", err
	}
	if len(hardwareInfo) == 0 {
		return "", nil
	}
	return hardwareInfo[0].Serial, nil
}
