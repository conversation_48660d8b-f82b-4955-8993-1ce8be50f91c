// Package service -----------------------------
// @file      : user_service.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/4/25 15:03
// -------------------------------------------
package service

import (
	"AgenticAI-Client/internal/auth"
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/event"
	"AgenticAI-Client/internal/global"
	"AgenticAI-Client/pkg/apiserver/dto"
	"AgenticAI-Client/pkg/apiserver/handler"
	"AgenticAI-Client/pkg/broker"
	"context"
	"fmt"
	"github.com/Authing/authing-golang-sdk/v3/authentication"
	"github.com/LB-AgenticAI/AgenticAI-Commons/emqx"
	"github.com/pkg/errors"
	"github.com/wailsapp/wails/v3/pkg/application"
	"go.uber.org/zap"
	"path"
	"sync"
)

var UserServiceImpl *UserService

type UserService struct {
	mu            sync.RWMutex
	c             *config.TuriXConfig
	Info          *userInfo
	broker        broker.Interface
	login         bool
	ac            *auth.Client
	app           *application.App
	logger        *zap.Logger
	permissionSrv handler.PermissionServiceHandler
}

func NewUserService(cfg *config.TuriXConfig, _broker broker.Interface, _app *application.App, psd handler.PermissionServiceHandler) *UserService {
	_info, err := getUserInfo(cfg.Path.DataDir)
	if err != nil {
		zap.L().Fatal("Failed to get user info", zap.Error(err))
	}

	us := &UserService{
		c:             cfg,
		Info:          _info,
		broker:        _broker,
		ac:            auth.NewClient(cfg),
		app:           _app,
		logger:        zap.L().With(zap.String("module", "userService")),
		permissionSrv: psd,
	}
	us.login = us.queryLoginStatus()
	UserServiceImpl = us

	return us
}

func (us *UserService) HandleLoginEvent(e *application.CustomEvent) {
	vals, ok := e.Data.([]interface{})
	if !ok {
		return
	}
	if len(vals) == 0 {
		return
	}
	val, ok := vals[0].(bool)
	if !ok {
		return
	}

	if val {
		// login
		res, err := us.ac.GenerateOIDCLoginURL(us.c.Server.RedirectUrl)
		if err != nil {
			us.logger.Error("Failed to generate login url", zap.Error(err))
			return
		}

		lw, _ := us.app.Window.Get(constant.WindowLogin)
		url := GetCloudUIURLWithCustomized(event.WindowShowCustomizeUrl, res.Url, "")
		lw.SetURL(url)
		lw.Show()

		return
	}

	// logout
	buildLogoutOpt := &authentication.BuildLogoutURLParams{
		PostLogoutRedirectUri: us.c.Server.RedirectLogoutUrl,
		IDTokenHint:           us.Info.Token.IDToken,
	}

	res, err := us.ac.GenerateOIDCLogoutURL(buildLogoutOpt)
	if err != nil {
		us.logger.Error("Failed to generate logout url", zap.Error(err))
		return
	}

	lw, _ := us.app.Window.Get(constant.WindowLogin)
	url := GetCloudUIURLWithCustomized(event.WindowShowCustomizeUrl, res, "")
	lw.SetURL(url)
	lw.Show()

	return
}

func (us *UserService) SetUserID(userID string) {
	// 发送设备绑定消息
	err := us.PublishDeviceBinding(true)
	if err != nil {
		us.logger.Error("Failed to publish device binding message", zap.Error(err))
	}

	// 触发broker重连
	err = us.broker.Relink()
	if err != nil {
		us.logger.Error("Failed to relink broker", zap.Error(err))
	}

	us.mu.Lock()
	us.Info.UserID = userID
	us.mu.Unlock()

	// upsert user info to db
	err = us.Info.Update()
	if err != nil {
		us.logger.Error("Failed to update user info", zap.Error(err))
	}

	// update task menu account item
	event.EmitEvent(constant.EventSystemTrayMenuChange, UserStatusLogin)
	us.logger.Debug("User logged in, userID: " + userID)
	us.login = true
}

func (us *UserService) SetUserInfo(detail *auth.UserInfo, token *authentication.OIDCTokenResponse) {
	us.mu.Lock()
	us.Info.Detail = *detail
	us.Info.Token = *token
	us.Info.UserID = us.Info.Detail.Sub
	us.mu.Unlock()

	// 触发broker重连
	err := us.broker.Relink()
	if err != nil {
		us.logger.Error("Failed to relink broker", zap.Error(err))
	}

	// 发送设备绑定消息
	err = us.PublishDeviceBinding(true)
	if err != nil {
		us.logger.Error("Failed to publish device binding message", zap.Error(err))
	}

	// upsert user info to db
	err = us.Info.Update()
	if err != nil {
		us.logger.Error("Failed to update user info", zap.Error(err))
	}

	// update task menu account item
	event.EmitEvent(constant.EventSystemTrayMenuChange, UserStatusLogin)
	us.logger.Debug("User logged in, userID: " + us.Info.UserID)
	us.login = true
}

func (us *UserService) GetUserInfo() (auth.UserInfo, authentication.OIDCTokenResponse) {
	us.mu.RLock()
	defer us.mu.RUnlock()

	return us.Info.Detail, us.Info.Token
}

func (us *UserService) GetUserID() string {
	us.mu.RLock()
	defer us.mu.RUnlock()

	return us.Info.UserID
}

func (us *UserService) GetDeviceID() string {
	us.mu.RLock()
	defer us.mu.RUnlock()

	return us.Info.Device.ID
}

func (us *UserService) Logout() error {
	if !us.login {
		return nil
	}

	// 发送设备解绑消息
	err := us.PublishDeviceBinding(false)
	if err != nil {
		return err
	}

	uid := us.GetUserID()

	us.mu.Lock()
	us.Info.UserID = ""
	us.Info.Detail = auth.UserInfo{}
	us.Info.Token = authentication.OIDCTokenResponse{}
	us.mu.Unlock()

	// 触发broker重连
	err = us.broker.Relink()
	if err != nil {
		return fmt.Errorf("failed to relink broker: %w", err)
	}

	// upsert user info to db
	err = us.Info.Update()
	if err != nil {
		return fmt.Errorf("failed to update user info: %w", err)
	}

	// update task menu account item
	event.EmitEvent(constant.EventSystemTrayMenuChange, UserStatusLogout)
	us.logger.Debug("User logged out, userID: " + uid)
	us.login = false

	return nil
}

func (us *UserService) IsLogin() bool {
	return us.login
}

func (us *UserService) LoginHandler(ctx context.Context, req *dto.LoginReq) (*dto.LoginRsp, error) {
	res, err := us.ac.GetAccessTokenByCode(req.Code)
	if err != nil {
		return nil, err
	}

	_userInfo, err := us.ac.GetUserInfoByAccessToken(res.AccessToken)
	if err != nil {
		return nil, err
	}

	us.SetUserInfo(_userInfo, res)

	return &dto.LoginRsp{}, nil
}

func (us *UserService) LogoutHandler(context.Context, *dto.LogoutReq) (*dto.LogoutRsp, error) {
	err := us.Logout()
	if err != nil {
		return nil, err
	}
	return &dto.LogoutRsp{}, nil
}

func (us *UserService) GetBrokerDeviceTopic() string {
	return fmt.Sprintf(constant.TopicChatTask, us.GetDeviceID())
}

func (us *UserService) GenerateHeartbeatResponse() (*emqx.DeviceHeartbeatResponse, error) {
	if us == nil || us.Info == nil {
		return nil, nil
	}
	if us.Info.Device == nil {
		var err error
		us.Info.Device, err = NewDevice()
		if err != nil {
			return nil, err
		}
	}

	rsp := &emqx.DeviceHeartbeatResponse{
		Code:   us.Info.Device.ID,
		UserID: us.GetUserID(),
	}

	return rsp, nil
}

// PublishDeviceBinding 发送设备绑定信息
// 当设备绑定成功后，需要发送设备绑定信息到broker
// binding 为 true 表示设备绑定成功，false 表示设备解绑成功
func (us *UserService) PublishDeviceBinding(binding bool) error {
	dRsp, err := us.generateDeviceResponse(binding)
	if err != nil {
		return fmt.Errorf("failed to generate device response with %w, binding %t", err, binding)
	}
	err = us.broker.PublishDeviceBindingReply(dRsp)
	if err != nil {
		return fmt.Errorf("failed to publish device binding reply with %w, binding %t", err, binding)
	}
	return nil
}

func (us *UserService) generateDeviceResponse(isLogin bool) (*emqx.DeviceResponse, error) {
	if us == nil || us.Info == nil {
		return nil, nil
	}
	if us.Info.Device == nil {
		var err error
		us.Info.Device, err = NewDevice()
		if err != nil {
			return nil, err
		}
	}

	res, err := us.queryPermission()
	if err != nil {
		return nil, errors.Wrap(err, "Failed to check permission")
	}

	rsp := &emqx.DeviceResponse{
		Code:                    us.Info.Device.ID,
		Name:                    us.Info.Device.Name,
		Type:                    us.Info.Device.OS.Name,
		IP:                      us.Info.Device.IP,
		UserID:                  us.GetUserID(),
		Status:                  isLogin,
		AccessibilityPermission: res.AccessibilityPermission,
		ScreenRecordPermission:  res.ScreenshootPermission,
	}

	return rsp, nil
}

// queryPermission macos 查询设备是否有权限
func (us *UserService) queryPermission() (*dto.PermissionCheckResponse, error) {
	checkRsp, err := us.permissionSrv.PermissionCheck(context.Background(), &dto.Null{})
	if err != nil {
		return nil, err
	}

	return checkRsp, nil
}

// queryLoginStatus 查询登录状态
func (us *UserService) queryLoginStatus() bool {
	if len(us.Info.Token.AccessToken) == 0 {
		return false
	}
	_, err := us.ac.IntrospectAccessTokenOffline(us.Info.Token.AccessToken)
	if err != nil {
		us.logger.Error("queryLoginStatus", zap.Error(err))
		return false
	}
	return true
}

func (us *UserService) HandleDeviceSettingRequest(request *emqx.DeviceSettingsRequest) error {
	// 处理设备设置请求
	// 唤起客户端设置页，就是说将这些参数同步到内置UI即可
	us.logger.Info("HandleDeviceSettingRequest", zap.Any("request", request))
	query := fmt.Sprintf("ui_theme=%s&desktop_assistant=%s&no_confirm_mode=%s&ux_improve_plan=%s", request.UiTheme, request.DesktopAssistant, request.NoConfirmMode, request.UxImprovePlan)
	event.EmitEvent(constant.EventMainWindow, event.WindowShowSetting, query)
	return nil
}

type UserStatus uint8

const (
	UserStatusLogin UserStatus = iota
	UserStatusLogout
	FloatBallShow
	FloatBallHide
)

var innerUIWindowEventSet = map[event.WindowStatus]struct{}{
	event.WindowShowSetting:                 {},
	event.WindowShowBackdropTransparentPage: {},
	event.WindowShowFloating:                {},
}

func GetCloudUIURLWithCustomized(status event.WindowStatus, url, query string) string {
	if status == event.WindowShowCustomizeUrl && len(url) > 0 {
		_, oidc := UserServiceImpl.GetUserInfo()
		deviceID := UserServiceImpl.GetDeviceID()
		if len(query) == 0 {
			return fmt.Sprintf("%s?access_token=%s&id_token=%s&refresh_token=%s&expires_in=%d&device=%s", url, oidc.AccessToken, oidc.IDToken, oidc.RefreshToken, oidc.ExpiresIn, deviceID)
		}

		return fmt.Sprintf("%s?access_token=%s&id_token=%s&refresh_token=%s&expires_in=%d&device=%s&%s", url, oidc.AccessToken, oidc.IDToken, oidc.RefreshToken, oidc.ExpiresIn, deviceID, query)
	}

	_, oidc := UserServiceImpl.GetUserInfo()
	deviceID := UserServiceImpl.GetDeviceID()

	var rawUrl string
	switch status {
	case event.WindowShowHome:
		rawUrl = constant.CloudWebUIHomeURL
	case event.WindowShowNewTask:
		rawUrl = constant.CloudWebUITaskURL
	case event.WindowShowSetting:
		rawUrl = constant.InnerUISettingURL
	case event.WindowShowBackdropTransparentPage:
		rawUrl = constant.InnerUIBackdropTransparentPage
	case event.WindowShowFloating:
		rawUrl = constant.InnerUIFloatingPage
	default:
		rawUrl = constant.CloudWebUITaskURL
	}

	_, ok := innerUIWindowEventSet[status]
	if !ok {
		cfg := config.GetTuriXConfig()
		isDev := global.IsDev()
		_url := path.Join(cfg.Base.CloudWebUIURL, rawUrl)
		if isDev {
			_url = path.Join(cfg.Base.CloudWebUIURLDev, rawUrl)
		}
		if !UserServiceImpl.IsLogin() {
			return _url
		}
		rawUrl = _url
	}
	if len(query) == 0 {
		return fmt.Sprintf("%s?access_token=%s&id_token=%s&refresh_token=%s&expires_in=%d&device=%s", rawUrl, oidc.AccessToken, oidc.IDToken, oidc.RefreshToken, oidc.ExpiresIn, deviceID)
	}
	return fmt.Sprintf("%s?access_token=%s&id_token=%s&refresh_token=%s&expires_in=%d&device=%s&%s", rawUrl, oidc.AccessToken, oidc.IDToken, oidc.RefreshToken, oidc.ExpiresIn, deviceID, query)
}

func GetCloudUIURL(status event.WindowStatus) string {
	_, oidc := UserServiceImpl.GetUserInfo()
	deviceID := UserServiceImpl.GetDeviceID()

	var rawUrl string
	switch status {
	case event.WindowShowHome:
		rawUrl = constant.CloudWebUIHomeURL
	case event.WindowShowNewTask:
		rawUrl = constant.CloudWebUITaskURL
	case event.WindowShowSetting:
		rawUrl = constant.InnerUISettingURL
	case event.WindowShowBackdropTransparentPage:
		rawUrl = constant.InnerUIBackdropTransparentPage
	case event.WindowShowFloating:
		rawUrl = constant.InnerUIFloatingPage
	default:
		rawUrl = constant.CloudWebUITaskURL
	}

	_, ok := innerUIWindowEventSet[status]
	if !ok {
		cfg := config.GetTuriXConfig()
		isDev := global.IsDev()
		_url := path.Join(cfg.Base.CloudWebUIURL, rawUrl)
		if isDev {
			_url = path.Join(cfg.Base.CloudWebUIURLDev, rawUrl)
		}
		if !UserServiceImpl.IsLogin() {
			return _url
		}
		rawUrl = _url
	}

	return fmt.Sprintf("%s?access_token=%s&id_token=%s&refresh_token=%s&expires_in=%d&device=%s", rawUrl, oidc.AccessToken, oidc.IDToken, oidc.RefreshToken, oidc.ExpiresIn, deviceID)
}
