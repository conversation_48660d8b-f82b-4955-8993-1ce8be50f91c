// Package service -----------------------------
// @file      : updateservice.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/14 11:27
// -------------------------------------------
package service

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/internal/constant"
	"AgenticAI-Client/internal/global"
	"AgenticAI-Client/pkg/selfupdate/check"
	"AgenticAI-Client/pkg/selfupdate/pack"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"os"
	"path"
	"sync"
)

var UpdateServiceImpl *UpdateService

type UpdateService struct {
	mu           sync.RWMutex
	c            *config.TuriXConfig
	Info         *UpdateStatus
	appUpdater   *check.Updater
	agentUpdater *check.Updater
}

func NewUpdateService(cfg *config.TuriXConfig) *UpdateService {
	if UpdateServiceImpl == nil {
		_info, err := getUpdateInfo(cfg)
		if err != nil {
			zap.L().Fatal("Failed to get update status info", zap.Error(err))
		}

		_appUpdater := &check.Updater{
			CurrentVersion: global.Version,
			ApiURL:         fmt.Sprintf(constant.UpdaterManifestUrl, constant.APPName, check.Plat),
			BinURL:         fmt.Sprintf(constant.UpdaterBinaryUrl, constant.APPName),
			DiffURL:        fmt.Sprintf(constant.UpdaterDiffUrl, constant.APPName),
			Dir:            path.Dir(cfg.Path.UpdateStatusInfoLocation),
			CmdName:        constant.APPName,
		}

		_agentUpdater := &check.Updater{
			CurrentVersion: global.Version,
			ApiURL:         fmt.Sprintf(constant.UpdaterManifestUrl, constant.AIAgentName, check.Plat),
			BinURL:         fmt.Sprintf(constant.UpdaterBinaryUrl, constant.AIAgentName),
			DiffURL:        fmt.Sprintf(constant.UpdaterDiffUrl, constant.AIAgentName),
			Dir:            path.Dir(cfg.Path.UpdateStatusInfoLocation),
			CmdName:        constant.AIAgentName,
		}

		us := &UpdateService{
			c:            cfg,
			Info:         _info,
			appUpdater:   _appUpdater,
			agentUpdater: _agentUpdater,
		}

		UpdateServiceImpl = us
	}

	return UpdateServiceImpl
}

func (us *UpdateService) Run() {
	// TODO implement me
}

func (us *UpdateService) SetUpdateStatus(info *UpdateStatus) error {
	us.mu.Lock()
	us.Info = info
	us.mu.Unlock()

	err := us.Info.Update()
	if err != nil {
		return fmt.Errorf("failed to update updateStatus: %w", err)
	}
	return nil
}

func (us *UpdateService) GetUpdateStatus() *UpdateStatus {
	us.mu.RLock()
	defer us.mu.RUnlock()

	return us.Info
}

type UpdateStatus struct {
	APPStatus    *pack.Manifest `json:"app_status"`
	AgentStatus  *pack.Manifest `json:"agent_status"`
	PendingApp   bool           `json:"pending_app"`
	PendingAgent bool           `json:"pending_agent"`
	Path         string         `json:"path"`
}

func getUpdateInfo(c *config.TuriXConfig) (*UpdateStatus, error) {
	// 检查更新状态文件是否存在
	_, err := os.Stat(c.Path.UpdateStatusInfoLocation)
	if os.IsNotExist(err) {
		// 文件不存在，生成默认更新状态文件
		_updateInfo := UpdateStatus{
			APPStatus:    &pack.Manifest{},
			AgentStatus:  &pack.Manifest{},
			PendingApp:   false,
			PendingAgent: false,
			Path:         c.Path.UpdateStatusInfoLocation,
		}

		var data []byte
		data, err = json.Marshal(&_updateInfo)
		if err != nil {
			return nil, fmt.Errorf("无法生成更新状态信息: %w", err)
		}

		err = os.WriteFile(c.Path.UpdateStatusInfoLocation, data, 0600)
		if err != nil {
			return nil, fmt.Errorf("无法写入更新状态文件: %w", err)
		}

		return &_updateInfo, nil
	}

	// 文件存在，读取更新状态信息
	data, err := os.ReadFile(c.Path.UpdateStatusInfoLocation)
	if err != nil {
		return nil, fmt.Errorf("无法读取更新状态文件: %w", err)
	}

	var _updateInfo UpdateStatus
	err = json.Unmarshal(data, &_updateInfo)
	if err != nil {
		return nil, fmt.Errorf("无法解析更新状态文件: %w", err)
	}

	return &_updateInfo, nil
}

func (p *UpdateStatus) Update() error {
	if p == nil {
		return nil
	}

	data, err := json.Marshal(p)
	if err != nil {
		return fmt.Errorf("无法更新状态信息: %w", err)
	}

	err = os.WriteFile(p.Path, data, 0600)
	if err != nil {
		return fmt.Errorf("无法写入更新状态文件: %w", err)
	}

	return nil
}
