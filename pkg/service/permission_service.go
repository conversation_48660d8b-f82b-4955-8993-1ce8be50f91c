// Package service -----------------------------
// @file      : permission_service.go
// <AUTHOR> jzechen
// @contact   : <EMAIL>
// @time      : 2025/5/28 13:17
// -------------------------------------------
package service

import (
	"AgenticAI-Client/internal/config"
	"AgenticAI-Client/pkg/apiserver/dto"
	"AgenticAI-Client/pkg/utils"
	pb "AgenticAI-Client/pkg/workflow/gen"
	"context"
	"fmt"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"io"
)

var PermissionServiceImpl *PermissionService

type PermissionService struct {
	cfg                 *config.TuriXConfig
	conn                *grpc.ClientConn
	permissionSrvClient pb.ChatServiceClient
	logger              *zap.Logger
}

func NewPermissionService(c *config.TuriXConfig) *PermissionService {
	_conn, err := utils.NewGrpcClient(c.AIAgentServer.Host, c.AIAgentServer.Port)
	if err != nil {
		panic(err)
	}

	PermissionServiceImpl = &PermissionService{
		cfg:                 c,
		conn:                _conn,
		permissionSrvClient: pb.NewChatServiceClient(_conn),
		logger:              zap.L().With(zap.String("module", "PermissionService")),
	}
	return PermissionServiceImpl
}

func (ps *PermissionService) PermissionCheck(ctx context.Context, null *dto.Null) (*dto.PermissionCheckResponse, error) {
	rsp := dto.PermissionCheckResponse{
		AccessibilityPermission: utils.HasAccessibilityPermission(),
		ScreenshootPermission:   utils.HasScreenCapturePermission(),
	}

	return &rsp, nil
}

func (ps *PermissionService) PermissionRequest(ctx context.Context, null *dto.Null) (string, error) {
	hasAccessibility := utils.HasAccessibilityPermission()
	if !hasAccessibility {
		utils.RequestAccessibilityPermission()
		return "RequestAccessibilityPermission successfully", nil
	}

	hasScreenCapture := utils.HasScreenCapturePermission()
	if !hasScreenCapture {
		utils.RequestScreenCapturePermission()
		return "RequestScreenCapturePermission successfully", nil
	}
	return "PermissionRequest successfully", nil
}

func (ps *PermissionService) PermissionToAccess(ctx context.Context, null *dto.Null) (string, error) {
	err := utils.OpenAccessibilitySettings()
	if err != nil {
		return "", err
	}
	return "OpenAccessibilitySettings successfully", nil
}

func (ps *PermissionService) PermissionToScreen(ctx context.Context, null *dto.Null) (string, error) {
	err := utils.OpenScreenCaptureSettings()
	if err != nil {
		return "", err
	}
	return "OpenScreenCaptureSettings successfully", nil
}

// Deprecated: use utils.permission.go instead
func (ps *PermissionService) permissionStream(ctx context.Context, req *pb.ChatRequest) (string, error) {
	// 建立流
	streamCtx, cancel := context.WithCancel(ctx)
	stream, err := ps.permissionSrvClient.Chat(streamCtx)
	if err != nil {
		cancel()
		return "", fmt.Errorf("start permisstion stream failed with %w", err)
	}
	defer cancel()

	ps.logger.Debug("send permission request to agent", zap.Any("request", req))

	err = stream.Send(req)
	if err != nil {
		if err == io.EOF {
			return "", nil
		}
		return "", fmt.Errorf("stream.Send failed failed with %w", err)
	}

	chunk, err := stream.Recv()
	if err != nil {
		if err == io.EOF {
			return "", nil
		}
		return "", fmt.Errorf("stream.Receive failed with %w", err)
	}
	ps.logger.Debug("Received chunk", zap.Any("chunk", chunk))

	err = stream.CloseSend()
	if err != nil {
		return "", fmt.Errorf("stream.CloseSend failed with %w", err)
	}

	textRes := chunk.GetTextResponse()
	if textRes != nil {
		return textRes.Content, nil
	}

	return "", nil
}
