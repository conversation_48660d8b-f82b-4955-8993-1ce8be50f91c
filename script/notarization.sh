#!/bin/bash
set -e

ZIP="$APP.zip"
PROFILE="ljkj"

# 提交
ID=$(xcrun notarytool submit "$ZIP" --keychain-profile "$PROFILE" --no-wait --no-s3-acceleration --output-format json | jq -r .id)
echo "Notary job submitted: $ID"

# 轮询
while true; do
  STATUS=$(xcrun notarytool info "$ID" --keychain-profile "$PROFILE" \
    --output-format json | jq -r .status)
  echo "$(date +'%H:%M:%S') Status: $STATUS"
  if [[ "$STATUS" != "In Progress" ]]; then break; fi
  sleep 30
done

# 如果 Accepted，就 staple
if [[ "$STATUS" == "Accepted" ]]; then
  xcrun stapler staple "$APP"
  echo "Stapled successfully."
  rm -rf "$ZIP"
  ditto -c -k --keepParent "$APP" "$ZIP"
else
  echo "Notarization failed with status: $STATUS"
  xcrun notarytool log "$ID" --keychain-profile "$PROFILE"
  exit 1
fi
