{"extends": "@tsconfig/node22/tsconfig.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "lib": ["esnext"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "noEmit": true}, "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "eslint.config.*", "tailwind.config.*"]}