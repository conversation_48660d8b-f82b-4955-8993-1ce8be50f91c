{"name": "turix", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "wails3:dev": "cd ../ && wails3 dev", "dev:plugin:create-icon-set": "node ./plugins/createIconSet.ts", "build:plugin:create-icon-set": "node ./plugins/createIconSet.ts --mode production", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "styleguide:build": "vue-docgen"}, "dependencies": {"@alova/adapter-axios": "^2.0.15", "@formkit/auto-animate": "^0.8.2", "@primeuix/themes": "^1.1.1", "@primevue/forms": "^4.3.5", "@tailwindcss/vite": "^4.1.10", "@vueuse/components": "^13.3.0", "@vueuse/core": "^13.3.0", "@vueuse/head": "^2.0.0", "@vueuse/math": "^13.3.0", "@wailsio/runtime": "latest", "alova": "^3.3.2", "axios": "^1.10.0", "chart.js": "^4.5.0", "colorsea": "^1.2.2", "es-toolkit": "^1.39.5", "motion-v": "^1.2.1", "pinia": "^3.0.3", "primevue": "^4.3.5", "tailwindcss": "^4.1.10", "tailwindcss-primeui": "^0.6.1", "type-fest": "^4.41.0", "vue": "^3.5.16", "vue-component-type-helpers": "^2.2.10", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1", "vue-virtual-scroller": "2.0.0-beta.8", "zod": "^3.25.0"}, "devDependencies": {"@alova/wormhole": "^1.0.7", "@antfu/eslint-config": "^4.14.1", "@egoist/tailwindcss-icons": "^1.9.0", "@eslint/eslintrc": "^3.3.1", "@iconify/json": "^2.2.349", "@iconify/tailwind4": "^1.0.6", "@iconify/tools": "^4.1.2", "@iconify/utils": "^2.3.0", "@iconify/vue": "^5.0.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@primevue/auto-import-resolver": "^4.3.5", "@tsconfig/node22": "^22.0.2", "@types/eslint-plugin-tailwindcss": "^3.17.0", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "chalk": "^5.4.1", "eslint": "^9.29.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-oxlint": "^0.16.12", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vue": "~10.0.1", "eslint-plugin-vuejs-accessibility": "^2.4.1", "fs-extra": "^11.3.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.12", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vite-plugin-devtools-json": "^0.2.0", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-pwa": "^1.0.1", "vite-plugin-vue-devtools": "^7.7.7", "vue-docgen-cli": "^4.79.0", "vue-tsc": "^2.2.10"}}