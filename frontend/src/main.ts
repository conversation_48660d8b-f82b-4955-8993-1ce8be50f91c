import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import { createApp } from 'vue'
import App from '@/App.vue'
import { i18n } from '@/locales'
import { router } from '@/router'
import '@/assets/main.css'

const app = createApp(App)
  .use(createPinia())
  .use(router)
  .use(i18n)
  .use(PrimeVue, {
    theme: {
      options: {
        cssLayer: {
          name: 'primevue',
          order: 'base, primevue, utilities',
        },
        darkModeSelector: '.dark',
        prefix: 'p',
      },
    },
  })

app.config.globalProperties.$api = $api

if (import.meta.env.DEV) {
  app.config.globalProperties.$log = (...data: Parameters<typeof console.log>) => {
    // eslint-disable-next-line no-console
    console.log(...data)
  }

  // @ts-expect-error 仅测试用
  window.$log = (...args) => {
    watchEffect(() => {
    // eslint-disable-next-line no-console
      console.log(...args.map(arg => toValue(arg)))
    })
  }
}
app.mount('#app')
