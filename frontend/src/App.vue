<script setup lang="ts">
const params = useUrlSearchParams<Record<string, string>>('history')

if (params.device) {
  localStorage.setItem('access_token', params.access_token)
  localStorage.setItem('id_token', params.id_token)
  localStorage.setItem('refresh_token', params.refresh_token)
  localStorage.setItem('expire_time', params.expire_time)
  localStorage.setItem('device-id', params.device)
}

if (params.desktop_assistant) {
  localStorage.setItem('desktop_assistant', params.desktop_assistant)
}
</script>

<template>
  <provider-message>
    <router-view />
    <!-- <router-view v-if="params.device" />
    <div v-else>
      error: no device id
    </div> -->
  </provider-message>
</template>
