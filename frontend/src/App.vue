<script setup lang="ts">
const params = useUrlSearchParams<Record<string, string>>('history')
const client = useClient()

if (params.device) {
  localStorage.setItem('access_token', params.access_token)
  localStorage.setItem('id_token', params.id_token)
  localStorage.setItem('refresh_token', params.refresh_token)
  localStorage.setItem('expire_time', params.expire_time)
  localStorage.setItem('device-id', params.device)
}

if (params.desktop_assistant) {
  localStorage.setItem('desktop_assistant', params.desktop_assistant)
}

// 监听EventBus事件（替代MQTT）
onMounted(() => {
  // 监听聊天响应
  client.on('event.chat.response', (response: any) => {
    console.log('收到聊天响应:', response)
    // 这里可以处理聊天响应，比如更新UI、显示消息等
  })

  // 监听设备状态
  client.on('event.device.status', (status: any) => {
    console.log('收到设备状态:', status)
    // 这里可以处理设备状态变化
  })

  // 监听设备心跳
  client.on('event.device.heartbeat', (heartbeat: any) => {
    console.log('收到设备心跳:', heartbeat)
    // 这里可以处理设备心跳信息
  })
})
</script>

<template>
  <provider-message>
    <router-view />
    <!-- <router-view v-if="params.device" />
    <div v-else>
      error: no device id
    </div> -->
  </provider-message>
</template>
