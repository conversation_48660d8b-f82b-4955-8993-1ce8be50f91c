<script setup lang="ts">
import { usePreset } from '@primeuix/themes'
import { Window } from '@wailsio/runtime'
import AuraSlate from '@/themes/AuraSlate'

useDark().value = true
usePreset(AuraSlate)

definePage({
  redirect: '/float/input',
})

// Window.SetPosition(500, 500)
</script>

<template>
  <div v-element-size="({ height, width }) => height && width && Window.SetSize(width, height)" class="inline-flex size-auto">
    <div class="p-[14px] starting:p-0">
      <div class="rounded-[20px] shadow-[inset_1.2px_1.2px_0.75px_0px_rgba(255,255,255,0.65),inset_-1.2px_-1.2px_0.75px_0px_rgba(255,255,255,0.65)] relative group/float w-160 starting:w-0">
        <router-view v-slot="{ Component }">
          <transition
            enter-active-class="animate-accordion-down overflow-hidden p-0"
            leave-active-class="animate-accordion-up overflow-hidden"
          >
            <component :is="Component" />
          </transition>
        </router-view>

        <p-button
          icon="icon-sys-icon-minus flex-none size-2"
          rounded
          severity="glass"
          class="size-4 absolute fill-mode-forwards group-hover/float:animate-in animate-out fade-in fade-out flex top-0 right-0"
          @click="Window.Close()"
        />
      </div>
    </div>
  </div>
</template>
