<script setup lang="ts">
const isModify = ref(false)

const text = ref('')
</script>

<template>
  <p-card class="bg-transparent" pt:content:class="">
    <template #content>
      <div class="flex flex-col gap-2 group/dialog-content size-full rounded-2xl" :class="isModify ? 'p-4 bg-[#0003]' : ''">
        <div class="flex justify-between items-center gap-2">
          <div class="font-bold">
            Book a Japanese restaurant at 19:00
          </div>
          <p-button
            icon="icon-[uil--setting] flex-none size-2"
            rounded
            class="bg-[#ccc] size-6 group-hover/dialog-content:opacity-100 opacity-0 transition-opacity"
            @click="$router.push('/float/input')"
          />
        </div>
        <div>Open AAA.com</div>
        <div>Select the most popular Japanese restaurant in ShenZhen</div>
        <div>Book 1 seats at 19:00</div>
      </div>
    </template>
    <template #footer>
      <div v-if="isModify" class="animate fade-in">
        <div class="flex items-center gap-4">
          <p-input-text
            v-model="text"
            class="w-[20em] overflow-hidden overflow-ellipsis text-nowrap focus:outline-none"
            placeholder="请输入你的任务"
            unstyled
            @keydown.enter="isModify = false"
          />
          <div class="flex-1 flex justify-end">
            <p-tag
              :class="{ 'opacity-0 select-none': !text }"
              dt=""
              severity="secondary"
              value="按Enter发送"
              class="bg-transparent text-nowrap cursor-default"
            />
          </div>

          <p-button
            icon="icon-[uil--setting] flex-none size-6"
            rounded
            size="small"
            class="bg-[#666] border-none"
          />
        </div>
      </div>
      <div v-else class="flex gap-4 justify-end animate fade-in">
        <p-button
          label="Modify"
          severity="secondary"
          variant="outlined"
          size="small"
          @click="isModify = true"
        />
        <p-button
          variant="outlined"
          severity="primary"
          label="Accept"
          size="small"
        />
      </div>
    </template>
  </p-card>
</template>
