<script setup lang="ts">
const text = ref('')
const voiceText = ref('')
const client = useClient()
const chatResponses = ref<any[]>([])

// 监听聊天响应事件
onMounted(() => {
  client.on('event.chat.response', (response: any) => {
    console.log('收到聊天响应:', response)
    chatResponses.value.push(response)
    // 可以在这里处理聊天响应，比如跳转到对话页面
    // $router.push('/float/dialog')
  })
})
</script>

<template>
  <use-request
    :method="$api.post_v1_quick_tasks({
      data: {
        command: '',
        device_ids: [],
        schedule_type: 'once',
        user_id: '  ', // 后面去掉该字段
      },
    })"
    :config="{
      immediate: false,
    }"
  >
    <template #default="{ send }">
      <div class="flex items-center gap-4 p-4">
        <i class="icon-sys-turix-logo size-6" />
        <p-input-text
          :model-value="text + voiceText"
          class="w-[20em] overflow-hidden overflow-ellipsis text-nowrap focus:outline-none"
          :placeholder="$t('what-can-i-help-you')"
          unstyled
          @update:model-value="text = $event ?? ''"
          @keydown.enter="send"
        />
        <div class="flex-1 flex justify-end">
          <p-tag
            :class="{ 'opacity-0 select-none': !text }"
            severity="secondary"
            :value="$t('press-enter')"
            class="bg-transparent text-nowrap cursor-default"
          />
        </div>

        <feature-button-speech-recognition
          class="flex-none"
          rounded
          severity="glass"
          size="small"
          @final="() => text += voiceText"
          @input="voiceText = $event"
        />
        <p-button
          icon="icon-sys-icon-setting flex-none"
          rounded
          severity="glass"
          size="small"
        />
      </div>
    </template>
  </use-request>
</template>
