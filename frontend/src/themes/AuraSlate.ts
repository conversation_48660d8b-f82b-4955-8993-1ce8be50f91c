import { definePreset } from '@primeuix/themes'
import Aura from '@primeuix/themes/aura'

export default definePreset(Aura, {
  components: {
    // custom button tokens and additional style
    button: {
      css: ({ dt }) => `
.p-button-glass {
    background: ${dt('button.glass.color')};
    color: ${dt('button.glass.inverse.color')};
    border: none;
}
`,
      extend: {
        glass: {
          color: '#ffffff33',
          inverseColor: '#ffffff',
        },
      },
    },
  },
  semantic: {
    colorScheme: {
      dark: {
        highlight: {
          background: '{primary.50}',
          color: '{primary.950}',
          focusBackground: '{primary.300}',
          focusColor: '{primary.950}',
        },
        primary: {
          activeColor: '{primary.200}',
          color: '{primary.50}',
          contrastColor: '{primary.950}',
          hoverColor: '{primary.100}',
        },
      },
      light: {
        highlight: {
          background: '{primary.950}',
          color: '{primary.50}',
          focusBackground: '{primary.700}',
          focusColor: '{primary.50}',
        },
        primary: {
          activeColor: '{primary.800}',
          color: '{primary.950}',
          contrastColor: '{primary.50}',
          hoverColor: '{primary.900}',
        },
      },
    },
    primary: {
      50: '{slate.50}',
      100: '{slate.100}',
      200: '{slate.200}',
      300: '{slate.300}',
      400: '{slate.400}',
      500: '{slate.500}',
      600: '{slate.600}',
      700: '{slate.700}',
      800: '{slate.800}',
      900: '{slate.900}',
      950: '{slate.950}',
    },
  },
})
