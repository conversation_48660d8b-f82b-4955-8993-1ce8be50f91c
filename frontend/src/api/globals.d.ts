/* tslint:disable */
/* eslint-disable */
/**
 * AgenticAI-Cloud - version 1.0.0
 *
 *
 *
 * OpenAPI version: 3.0.1
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
import type { Alova, AlovaMethodCreateConfig, AlovaGenerics, Method } from 'alova';
import type { $$userConfigMap, alovaInstance } from '.';
import type apiDefinitions from './apiDefinitions';

type CollapsedAlova = typeof alovaInstance;
type UserMethodConfigMap = typeof $$userConfigMap;

type Alova2MethodConfig<Responded> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Omit<
        AlovaMethodCreateConfig<
          AlovaGenerics<Responded, any, RequestConfig, Response, ResponseHeader, L1Cache, L2Cache, SE>,
          any,
          Responded
        >,
        'params'
      >
    : never;

// Extract the return type of transform function that define in $$userConfigMap, if it not exists, use the default type.
type ExtractUserDefinedTransformed<
  DefinitionKey extends keyof typeof apiDefinitions,
  Default
> = DefinitionKey extends keyof UserMethodConfigMap
  ? UserMethodConfigMap[DefinitionKey]['transform'] extends (...args: any[]) => any
    ? Awaited<ReturnType<UserMethodConfigMap[DefinitionKey]['transform']>>
    : Default
  : Default;
type Alova2Method<
  Responded,
  DefinitionKey extends keyof typeof apiDefinitions,
  CurrentConfig extends Alova2MethodConfig<any>
> =
  CollapsedAlova extends Alova<
    AlovaGenerics<
      any,
      any,
      infer RequestConfig,
      infer Response,
      infer ResponseHeader,
      infer L1Cache,
      infer L2Cache,
      infer SE
    >
  >
    ? Method<
        AlovaGenerics<
          CurrentConfig extends undefined
            ? ExtractUserDefinedTransformed<DefinitionKey, Responded>
            : CurrentConfig['transform'] extends (...args: any[]) => any
              ? Awaited<ReturnType<CurrentConfig['transform']>>
              : ExtractUserDefinedTransformed<DefinitionKey, Responded>,
          any,
          RequestConfig,
          Response,
          ResponseHeader,
          L1Cache,
          L2Cache,
          SE
        >
      >
    : never;

export type ScheduleType = 'once' | 'daily' | 'monthly';
export type QuickTask = {
  /**
   * [required]
   */
  id: string;
  /**
   * [required]
   */
  description: string;
  /**
   * [required]
   */
  command: string;
  /**
   * once/daily/monthly
   * [required]
   */
  schedule_type: ScheduleType;
  /**
   * [required]
   */
  device_ids: string[] | null;
  /**
   * [required]
   */
  user_id: string;
  /**
   * [required]
   */
  last_executed: number;
};
export type Task = {
  /**
   * [required]
   */
  id: string;
  /**
   * [required]
   */
  description: string;
  /**
   * [required]
   */
  command: string;
  /**
   * [required]
   */
  plan: string;
  /**
   * pending/completed
   * [required]
   */
  plan_status: string;
  /**
   * [required]
   */
  steps: Array<{
    /**
     * ID 编号
     * [required]
     */
    id: string;
    /**
     * 标题
     * [required]
     */
    goal: string;
    /**
     * 轮播动作列表
     * [required]
     */
    actions: string[];
    /**
     * [required]
     */
    current_action: string;
    /**
     * [required]
     */
    result: string;
    /**
     * [required]
     */
    photo: string;
    /**
     * success/failed/running/pending/paused/completed
     * [required]
     */
    status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
  }> | null;
  /**
   * once/daily/monthly
   * [required]
   */
  schedule_type: ScheduleType;
  /**
   * success/failed/running/pending/paused/completed
   * [required]
   */
  status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
  /**
   * [required]
   */
  error: string;
  /**
   * [required]
   */
  result: string;
  /**
   * common/quick
   * [required]
   */
  type: 'common' | 'quick';
  /**
   * [required]
   */
  start_time: number;
  /**
   * [required]
   */
  quick_task_id: string | null;
  /**
   * [required]
   */
  device_id: string | null;
};
export type DeviceSettings = {
  /**
   * 取值：on,off
   */
  ui_theme?: string;
  /**
   * 取值：on,off
   */
  desktop_assistant?: string;
  /**
   * 取值：on,off
   */
  no_confirm_mode?: string;
  /**
   * 取值：on,off
   */
  ux_improve_plan?: string;
};
export type Device = {
  /**
   * [required]
   */
  code: string;
  /**
   * [required]
   */
  name: string;
  /**
   * [required]
   */
  is_online: boolean;
  /**
   * [required]
   */
  status: string;
  /**
   * [required]
   */
  type: string;
  /**
   * [required]
   */
  ip: string;
  /**
   * [required]
   */
  last_heartbeat: number;
  /**
   * [required]
   */
  last_login: number;
  /**
   * 辅助功能权限
   * [required]
   */
  accessibility_permission: boolean;
  /**
   * 屏幕录制权限
   * [required]
   */
  screen_record_permission: boolean;
  settings?: DeviceSettings;
};
export type User = {
  /**
   * [required]
   */
  id: string;
  create_time?: number;
  last_modify_time?: number;
  username?: string;
  nickname?: string;
  email?: string;
  /**
   * [required]
   */
  phone: string;
  /**
   * 用户头像url
   */
  avatar?: string;
  status?: number;
  last_login?: null;
  verified_at?: null;
  full_name?: string;
  agree_terms?: boolean;
  created_at?: string;
  updated_at?: string;
  /**
   * 取值：light
   */
  ui_theme?: string;
  /**
   * 取值：on,off
   */
  ux_improve_plan?: string;
  /**
   * 是否设置密码
   */
  is_set_password?: boolean;
  /**
   * 是否绑定手机
   */
  phone_number_verified?: boolean;
  /**
   * 用户名
   */
  user_name?: string;
  /**
   * 是否绑定邮箱
   */
  email_verified?: boolean;
};
declare global {
  interface TuriXApi {
    general: {
      /**
       * ---
       *
       * [GET] 查询快捷任务
       *
       * **path:** /v1/quick_tasks/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   id: string
       *   // [required]
       *   description: string
       *   // [required]
       *   command: string
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // [required]
       *   device_ids: string[] | null
       *   // [required]
       *   user_id: string
       *   // [required]
       *   last_executed: number
       * }
       * ```
       */
      get_v1_quick_tasks_id<
        Config extends Alova2MethodConfig<QuickTask> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<QuickTask, 'general.get_v1_quick_tasks_id', Config>;
      /**
       * ---
       *
       * [PUT] 更新快捷任务
       *
       * **path:** /v1/quick_tasks/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   description?: string
       *   command?: string
       *   // once/daily/monthly
       *   schedule_type?: 'once' | 'daily' | 'monthly'
       *   device_ids?: string[] | null
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      put_v1_quick_tasks_id<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
          data: {
            description?: string;
            command?: string;
            /**
             * once/daily/monthly
             */
            schedule_type?: ScheduleType;
            device_ids?: string[] | null;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.put_v1_quick_tasks_id', Config>;
      /**
       * ---
       *
       * [DELETE] 删除快捷任务
       *
       * **path:** /v1/quick_tasks/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      delete_v1_quick_tasks_id<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.delete_v1_quick_tasks_id', Config>;
      /**
       * ---
       *
       * [POST] 创建快捷任务
       *
       * **path:** /v1/quick_tasks
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   description?: string
       *   // [required]
       *   command: string
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // [required]
       *   device_ids: string[] | null
       *   // [required]
       *   user_id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // ID 编号
       *   // [required]
       *   id: string
       * }
       * ```
       */
      post_v1_quick_tasks<
        Config extends Alova2MethodConfig<{
          /**
           * ID 编号
           * [required]
           */
          id: string;
        }> & {
          data: {
            description?: string;
            /**
             * [required]
             */
            command: string;
            /**
             * once/daily/monthly
             * [required]
             */
            schedule_type: ScheduleType;
            /**
             * [required]
             */
            device_ids: string[] | null;
            /**
             * [required]
             */
            user_id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * ID 编号
           * [required]
           */
          id: string;
        },
        'general.post_v1_quick_tasks',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取所有快捷任务
       *
       * **path:** /v1/quick_tasks
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // [required]
       *   user_id: string
       *   // min：1
       *   // [required]
       *   page_size: number
       *   // min：1
       *   // [required]
       *   current_page: number
       *   // [required]
       *   query: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   result?: Array<{
       *     // [required]
       *     id: string
       *     // [required]
       *     description: string
       *     // [required]
       *     command: string
       *     // once/daily/monthly
       *     // [required]
       *     schedule_type: 'once' | 'daily' | 'monthly'
       *     // [required]
       *     device_ids: string[] | null
       *     // [required]
       *     user_id: string
       *     // [required]
       *     last_executed: number
       *   }>
       *   count?: number
       * }
       * ```
       */
      get_v1_quick_tasks<
        Config extends Alova2MethodConfig<{
          result?: Array<{
            /**
             * [required]
             */
            id: string;
            /**
             * [required]
             */
            description: string;
            /**
             * [required]
             */
            command: string;
            /**
             * once/daily/monthly
             * [required]
             */
            schedule_type: ScheduleType;
            /**
             * [required]
             */
            device_ids: string[] | null;
            /**
             * [required]
             */
            user_id: string;
            /**
             * [required]
             */
            last_executed: number;
          }>;
          count?: number;
        }> & {
          params: {
            /**
             * [required]
             */
            user_id: string;
            /**
             * min：1
             * [required]
             */
            page_size: number;
            /**
             * min：1
             * [required]
             */
            current_page: number;
            /**
             * [required]
             */
            query: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          result?: Array<{
            /**
             * [required]
             */
            id: string;
            /**
             * [required]
             */
            description: string;
            /**
             * [required]
             */
            command: string;
            /**
             * once/daily/monthly
             * [required]
             */
            schedule_type: ScheduleType;
            /**
             * [required]
             */
            device_ids: string[] | null;
            /**
             * [required]
             */
            user_id: string;
            /**
             * [required]
             */
            last_executed: number;
          }>;
          count?: number;
        },
        'general.get_v1_quick_tasks',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询历史记录
       *
       * **path:** /v1/chat_histories/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   name: string
       *   content?: Array<{
       *     // [required]
       *     id: string
       *     // [required]
       *     description: string
       *     // [required]
       *     command: string
       *     // [required]
       *     plan: string
       *     // pending/completed
       *     // [required]
       *     plan_status: string
       *     // [required]
       *     steps: Array<{
       *       // ID 编号
       *       // [required]
       *       id: string
       *       // 标题
       *       // [required]
       *       goal: string
       *       // 轮播动作列表
       *       // [required]
       *       actions: string[]
       *       // [required]
       *       current_action: string
       *       // [required]
       *       result: string
       *       // [required]
       *       photo: string
       *       // success/failed/running/pending/paused/completed
       *       // [required]
       *       status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     }> | null
       *     // once/daily/monthly
       *     // [required]
       *     schedule_type: 'once' | 'daily' | 'monthly'
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     // [required]
       *     error: string
       *     // [required]
       *     result: string
       *     // common/quick
       *     // [required]
       *     type: 'common' | 'quick'
       *     // [required]
       *     start_time: number
       *     // [required]
       *     quick_task_id: string | null
       *     // [required]
       *     device_id: string | null
       *   }>
       *   // [required]
       *   device_id: string
       * }
       * ```
       */
      get_v1_chat_histories_id<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          name: string;
          content?: Task[];
          /**
           * [required]
           */
          device_id: string;
        }> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          name: string;
          content?: Task[];
          /**
           * [required]
           */
          device_id: string;
        },
        'general.get_v1_chat_histories_id',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 更新历史记录
       *
       * **path:** /v1/chat_histories/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   name?: string
       *   content?: Array<{
       *     // [required]
       *     id: string
       *     // [required]
       *     description: string
       *     // [required]
       *     command: string
       *     // [required]
       *     plan: string
       *     // pending/completed
       *     // [required]
       *     plan_status: string
       *     // [required]
       *     steps: Array<{
       *       // ID 编号
       *       // [required]
       *       id: string
       *       // 标题
       *       // [required]
       *       goal: string
       *       // 轮播动作列表
       *       // [required]
       *       actions: string[]
       *       // [required]
       *       current_action: string
       *       // [required]
       *       result: string
       *       // [required]
       *       photo: string
       *       // success/failed/running/pending/paused/completed
       *       // [required]
       *       status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     }> | null
       *     // once/daily/monthly
       *     // [required]
       *     schedule_type: 'once' | 'daily' | 'monthly'
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     // [required]
       *     error: string
       *     // [required]
       *     result: string
       *     // common/quick
       *     // [required]
       *     type: 'common' | 'quick'
       *     // [required]
       *     start_time: number
       *     // [required]
       *     quick_task_id: string | null
       *     // [required]
       *     device_id: string | null
       *   }>
       *   device_id?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      put_v1_chat_histories_id<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
          data: {
            name?: string;
            content?: Task[];
            device_id?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.put_v1_chat_histories_id', Config>;
      /**
       * ---
       *
       * [DELETE] 删除历史记录
       *
       * **path:** /v1/chat_histories/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      delete_v1_chat_histories_id<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.delete_v1_chat_histories_id', Config>;
      /**
       * ---
       *
       * [POST] 创建历史记录
       *
       * **path:** /v1/chat_histories
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   name: string
       *   // [required]
       *   user_id: string
       *   // [required]
       *   device_id: string
       *   content?: Array<{
       *     // [required]
       *     id: string
       *     // [required]
       *     description: string
       *     // [required]
       *     command: string
       *     // [required]
       *     plan: string
       *     // pending/completed
       *     // [required]
       *     plan_status: string
       *     // [required]
       *     steps: Array<{
       *       // ID 编号
       *       // [required]
       *       id: string
       *       // 标题
       *       // [required]
       *       goal: string
       *       // 轮播动作列表
       *       // [required]
       *       actions: string[]
       *       // [required]
       *       current_action: string
       *       // [required]
       *       result: string
       *       // [required]
       *       photo: string
       *       // success/failed/running/pending/paused/completed
       *       // [required]
       *       status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     }> | null
       *     // once/daily/monthly
       *     // [required]
       *     schedule_type: 'once' | 'daily' | 'monthly'
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     // [required]
       *     error: string
       *     // [required]
       *     result: string
       *     // common/quick
       *     // [required]
       *     type: 'common' | 'quick'
       *     // [required]
       *     start_time: number
       *     // [required]
       *     quick_task_id: string | null
       *     // [required]
       *     device_id: string | null
       *   }>
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // ID 编号
       *   // [required]
       *   id: string
       * }
       * ```
       */
      post_v1_chat_histories<
        Config extends Alova2MethodConfig<{
          /**
           * ID 编号
           * [required]
           */
          id: string;
        }> & {
          data: {
            /**
             * [required]
             */
            name: string;
            /**
             * [required]
             */
            user_id: string;
            /**
             * [required]
             */
            device_id: string;
            content?: Task[];
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * ID 编号
           * [required]
           */
          id: string;
        },
        'general.post_v1_chat_histories',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取所有历史记录
       *
       * **path:** /v1/chat_histories
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // min：1
       *   // [required]
       *   page_size: number
       *   // min：1
       *   // [required]
       *   current_page: number
       *   query?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   result?: Array<{
       *     // [required]
       *     id: string
       *     // [required]
       *     name: string
       *     // [required]
       *     content: Array<{
       *       // [required]
       *       id: string
       *       // [required]
       *       description: string
       *       // [required]
       *       command: string
       *       // [required]
       *       plan: string
       *       // pending/completed
       *       // [required]
       *       plan_status: string
       *       // [required]
       *       steps: Array<{
       *         // ID 编号
       *         // [required]
       *         id: string
       *         // 标题
       *         // [required]
       *         goal: string
       *         // 轮播动作列表
       *         // [required]
       *         actions: string[]
       *         // [required]
       *         current_action: string
       *         // [required]
       *         result: string
       *         // [required]
       *         photo: string
       *         // success/failed/running/pending/paused/completed
       *         // [required]
       *         status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *       }> | null
       *       // once/daily/monthly
       *       // [required]
       *       schedule_type: 'once' | 'daily' | 'monthly'
       *       // success/failed/running/pending/paused/completed
       *       // [required]
       *       status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *       // [required]
       *       error: string
       *       // [required]
       *       result: string
       *       // common/quick
       *       // [required]
       *       type: 'common' | 'quick'
       *       // [required]
       *       start_time: number
       *       // [required]
       *       quick_task_id: string | null
       *       // [required]
       *       device_id: string | null
       *     }>
       *     // [required]
       *     device_id: string
       *   }>
       * }
       * ```
       */
      get_v1_chat_histories<
        Config extends Alova2MethodConfig<{
          result?: Array<{
            /**
             * [required]
             */
            id: string;
            /**
             * [required]
             */
            name: string;
            /**
             * [required]
             */
            content: Task[];
            /**
             * [required]
             */
            device_id: string;
          }>;
        }> & {
          params: {
            /**
             * min：1
             * [required]
             */
            page_size: number;
            /**
             * min：1
             * [required]
             */
            current_page: number;
            query?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          result?: Array<{
            /**
             * [required]
             */
            id: string;
            /**
             * [required]
             */
            name: string;
            /**
             * [required]
             */
            content: Task[];
            /**
             * [required]
             */
            device_id: string;
          }>;
        },
        'general.get_v1_chat_histories',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询设备信息
       *
       * **path:** /v1/devices/{code}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   code: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   name: string
       *   // [required]
       *   is_online: boolean
       *   // [required]
       *   status: string
       *   // [required]
       *   type: string
       *   // [required]
       *   ip: string
       *   // [required]
       *   last_heartbeat: number
       *   // [required]
       *   last_login: number
       *   // 辅助功能权限
       *   // [required]
       *   accessibility_permission: boolean
       *   // 屏幕录制权限
       *   // [required]
       *   screen_record_permission: boolean
       *   settings?: {
       *     // 取值：on,off
       *     ui_theme?: string
       *     // 取值：on,off
       *     desktop_assistant?: string
       *     // 取值：on,off
       *     no_confirm_mode?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *   }
       * }
       * ```
       */
      get_v1_devices_code<
        Config extends Alova2MethodConfig<Device> & {
          pathParams: {
            /**
             * [required]
             */
            code: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Device, 'general.get_v1_devices_code', Config>;
      /**
       * ---
       *
       * [DELETE] 删除设备信息
       *
       * **path:** /v1/devices/{code}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   code: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      delete_v1_devices_code<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            code: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.delete_v1_devices_code', Config>;
      /**
       * ---
       *
       * [PUT] 更新设备信息
       *
       * **path:** /v1/devices/{code}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   code: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   settings?: {
       *     // 取值：on,off
       *     ui_theme?: string
       *     // 取值：on,off
       *     desktop_assistant?: string
       *     // 取值：on,off
       *     no_confirm_mode?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *   }
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   name: string
       *   // [required]
       *   is_online: boolean
       *   // [required]
       *   status: string
       *   // [required]
       *   type: string
       *   // [required]
       *   ip: string
       *   // [required]
       *   last_heartbeat: number
       *   // [required]
       *   last_login: number
       *   // 辅助功能权限
       *   // [required]
       *   accessibility_permission: boolean
       *   // 屏幕录制权限
       *   // [required]
       *   screen_record_permission: boolean
       *   settings?: {
       *     // 取值：on,off
       *     ui_theme?: string
       *     // 取值：on,off
       *     desktop_assistant?: string
       *     // 取值：on,off
       *     no_confirm_mode?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *   }
       * }
       * ```
       */
      put_v1_devices_code<
        Config extends Alova2MethodConfig<Device> & {
          pathParams: {
            /**
             * [required]
             */
            code: string;
          };
          data: {
            settings?: DeviceSettings;
          };
        }
      >(
        config: Config
      ): Alova2Method<Device, 'general.put_v1_devices_code', Config>;
      /**
       * ---
       *
       * [GET] 获取所有设备信息
       *
       * **path:** /v1/devices
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // [required]
       *   query: string
       *   // min：1
       *   // [required]
       *   page_size: number
       *   // min：1
       *   // [required]
       *   current_page: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   result?: Array<{
       *     // [required]
       *     code: string
       *     // [required]
       *     name: string
       *     // [required]
       *     is_online: boolean
       *     // [required]
       *     status: string
       *     // [required]
       *     type: string
       *     // [required]
       *     ip: string
       *     // [required]
       *     last_heartbeat: number
       *     // [required]
       *     last_login: number
       *     // 辅助功能权限
       *     // [required]
       *     accessibility_permission: boolean
       *     // 屏幕录制权限
       *     // [required]
       *     screen_record_permission: boolean
       *     settings?: {
       *       // 取值：on,off
       *       ui_theme?: string
       *       // 取值：on,off
       *       desktop_assistant?: string
       *       // 取值：on,off
       *       no_confirm_mode?: string
       *       // 取值：on,off
       *       ux_improve_plan?: string
       *     }
       *   }>
       * }
       * ```
       */
      get_v1_devices<
        Config extends Alova2MethodConfig<{
          result?: Device[];
        }> & {
          params: {
            /**
             * [required]
             */
            query: string;
            /**
             * min：1
             * [required]
             */
            page_size: number;
            /**
             * min：1
             * [required]
             */
            current_page: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          result?: Device[];
        },
        'general.get_v1_devices',
        Config
      >;
      /**
       * ---
       *
       * [GET] 唤起设备配置
       *
       * **path:** /v1/devices/invoke/{code}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   code: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   name: string
       *   // [required]
       *   is_online: boolean
       *   // [required]
       *   status: string
       *   // [required]
       *   type: string
       *   // [required]
       *   ip: string
       *   // [required]
       *   last_heartbeat: number
       *   // [required]
       *   last_login: number
       *   // 辅助功能权限
       *   // [required]
       *   accessibility_permission: boolean
       *   // 屏幕录制权限
       *   // [required]
       *   screen_record_permission: boolean
       *   settings?: {
       *     // 取值：on,off
       *     ui_theme?: string
       *     // 取值：on,off
       *     desktop_assistant?: string
       *     // 取值：on,off
       *     no_confirm_mode?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *   }
       * }
       * ```
       */
      get_v1_devices_invoke_code<
        Config extends Alova2MethodConfig<Device> & {
          pathParams: {
            /**
             * [required]
             */
            code: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Device, 'general.get_v1_devices_invoke_code', Config>;
      /**
       * ---
       *
       * [GET] 唤起设备权限配置
       *
       * **path:** /v1/devices/perm/invoke/{code}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   code: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   code: string
       *   // [required]
       *   name: string
       *   // [required]
       *   is_online: boolean
       *   // [required]
       *   status: string
       *   // [required]
       *   type: string
       *   // [required]
       *   ip: string
       *   // [required]
       *   last_heartbeat: number
       *   // [required]
       *   last_login: number
       *   // 辅助功能权限
       *   // [required]
       *   accessibility_permission: boolean
       *   // 屏幕录制权限
       *   // [required]
       *   screen_record_permission: boolean
       *   settings?: {
       *     // 取值：on,off
       *     ui_theme?: string
       *     // 取值：on,off
       *     desktop_assistant?: string
       *     // 取值：on,off
       *     no_confirm_mode?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *   }
       * }
       * ```
       */
      get_v1_devices_perm_invoke_code<
        Config extends Alova2MethodConfig<Device> & {
          pathParams: {
            /**
             * [required]
             */
            code: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<Device, 'general.get_v1_devices_perm_invoke_code', Config>;
      /**
       * ---
       *
       * [GET] 查询任务信息
       *
       * **path:** /v1/tasks/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   id: string
       *   // [required]
       *   description: string
       *   // [required]
       *   command: string
       *   // [required]
       *   plan: string
       *   // pending/completed
       *   // [required]
       *   plan_status: string
       *   // [required]
       *   steps: Array<{
       *     // ID 编号
       *     // [required]
       *     id: string
       *     // 标题
       *     // [required]
       *     goal: string
       *     // 轮播动作列表
       *     // [required]
       *     actions: string[]
       *     // [required]
       *     current_action: string
       *     // [required]
       *     result: string
       *     // [required]
       *     photo: string
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *   }> | null
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // success/failed/running/pending/paused/completed
       *   // [required]
       *   status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *   // [required]
       *   error: string
       *   // [required]
       *   result: string
       *   // common/quick
       *   // [required]
       *   type: 'common' | 'quick'
       *   // [required]
       *   start_time: number
       *   // [required]
       *   quick_task_id: string | null
       *   // [required]
       *   device_id: string | null
       * }
       * ```
       */
      get_v1_tasks_id<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          id: string;
          /**
           * [required]
           */
          description: string;
          /**
           * [required]
           */
          command: string;
          /**
           * [required]
           */
          plan: string;
          /**
           * pending/completed
           * [required]
           */
          plan_status: string;
          /**
           * [required]
           */
          steps: Array<{
            /**
             * ID 编号
             * [required]
             */
            id: string;
            /**
             * 标题
             * [required]
             */
            goal: string;
            /**
             * 轮播动作列表
             * [required]
             */
            actions: string[];
            /**
             * [required]
             */
            current_action: string;
            /**
             * [required]
             */
            result: string;
            /**
             * [required]
             */
            photo: string;
            /**
             * success/failed/running/pending/paused/completed
             * [required]
             */
            status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          }> | null;
          /**
           * once/daily/monthly
           * [required]
           */
          schedule_type: ScheduleType;
          /**
           * success/failed/running/pending/paused/completed
           * [required]
           */
          status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          /**
           * [required]
           */
          error: string;
          /**
           * [required]
           */
          result: string;
          /**
           * common/quick
           * [required]
           */
          type: 'common' | 'quick';
          /**
           * [required]
           */
          start_time: number;
          /**
           * [required]
           */
          quick_task_id: string | null;
          /**
           * [required]
           */
          device_id: string | null;
        }> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          id: string;
          /**
           * [required]
           */
          description: string;
          /**
           * [required]
           */
          command: string;
          /**
           * [required]
           */
          plan: string;
          /**
           * pending/completed
           * [required]
           */
          plan_status: string;
          /**
           * [required]
           */
          steps: Array<{
            /**
             * ID 编号
             * [required]
             */
            id: string;
            /**
             * 标题
             * [required]
             */
            goal: string;
            /**
             * 轮播动作列表
             * [required]
             */
            actions: string[];
            /**
             * [required]
             */
            current_action: string;
            /**
             * [required]
             */
            result: string;
            /**
             * [required]
             */
            photo: string;
            /**
             * success/failed/running/pending/paused/completed
             * [required]
             */
            status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          }> | null;
          /**
           * once/daily/monthly
           * [required]
           */
          schedule_type: ScheduleType;
          /**
           * success/failed/running/pending/paused/completed
           * [required]
           */
          status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          /**
           * [required]
           */
          error: string;
          /**
           * [required]
           */
          result: string;
          /**
           * common/quick
           * [required]
           */
          type: 'common' | 'quick';
          /**
           * [required]
           */
          start_time: number;
          /**
           * [required]
           */
          quick_task_id: string | null;
          /**
           * [required]
           */
          device_id: string | null;
        },
        'general.get_v1_tasks_id',
        Config
      >;
      /**
       * ---
       *
       * [PUT] 更新任务信息
       *
       * **path:** /v1/tasks/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   description: string
       *   // [required]
       *   command: string
       *   // [required]
       *   plan: string
       *   // [required]
       *   steps: Array<{
       *     // ID 编号
       *     // [required]
       *     id: string
       *     // 标题
       *     // [required]
       *     goal: string
       *     // 轮播动作列表
       *     // [required]
       *     actions: string[]
       *     // [required]
       *     current_action: string
       *     // [required]
       *     result: string
       *     // [required]
       *     photo: string
       *     // success/failed/running/pending/paused
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused'
       *   }> | null
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // success/failed/running/pending/paused/completed
       *   // [required]
       *   status: 'pending' | 'running' | 'success' | 'failed' | 'paused'
       *   // common/quick
       *   // [required]
       *   type: 'common' | 'quick'
       *   // [required]
       *   device_id: string | null
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      put_v1_tasks_id<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
          data: {
            /**
             * [required]
             */
            description: string;
            /**
             * [required]
             */
            command: string;
            /**
             * [required]
             */
            plan: string;
            /**
             * [required]
             */
            steps: Array<{
              /**
               * ID 编号
               * [required]
               */
              id: string;
              /**
               * 标题
               * [required]
               */
              goal: string;
              /**
               * 轮播动作列表
               * [required]
               */
              actions: string[];
              /**
               * [required]
               */
              current_action: string;
              /**
               * [required]
               */
              result: string;
              /**
               * [required]
               */
              photo: string;
              /**
               * success/failed/running/pending/paused
               * [required]
               */
              status: 'pending' | 'running' | 'success' | 'failed' | 'paused';
            }> | null;
            /**
             * once/daily/monthly
             * [required]
             */
            schedule_type: ScheduleType;
            /**
             * success/failed/running/pending/paused/completed
             * [required]
             */
            status: 'pending' | 'running' | 'success' | 'failed' | 'paused';
            /**
             * common/quick
             * [required]
             */
            type: 'common' | 'quick';
            /**
             * [required]
             */
            device_id: string | null;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.put_v1_tasks_id', Config>;
      /**
       * ---
       *
       * [DELETE] 删除任务信息
       *
       * **path:** /v1/tasks/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      delete_v1_tasks_id<
        Config extends Alova2MethodConfig<unknown> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.delete_v1_tasks_id', Config>;
      /**
       * ---
       *
       * [POST] 创建任务信息
       *
       * **path:** /v1/tasks
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   description: string
       *   // [required]
       *   command: string
       *   // [required]
       *   plan: string
       *   // [required]
       *   steps: Array<{
       *     // ID 编号
       *     // [required]
       *     id: string
       *     // 标题
       *     // [required]
       *     goal: string
       *     // 轮播动作列表
       *     // [required]
       *     actions: string[]
       *     // [required]
       *     current_action: string
       *     // [required]
       *     result: string
       *     // [required]
       *     photo: string
       *     // success/failed/running/pending/paused
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused'
       *   }> | null
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // success/failed/running/pending/paused/completed
       *   // [required]
       *   status: 'pending' | 'running' | 'success' | 'failed' | 'paused'
       *   // [required]
       *   result: string
       *   // common/quick
       *   // [required]
       *   type: 'common' | 'quick'
       *   // [required]
       *   start_time: number
       *   // [required]
       *   quick_task_id: string | null
       *   // [required]
       *   device_id: string | null
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   id: string
       *   // [required]
       *   description: string
       *   // [required]
       *   command: string
       *   // [required]
       *   plan: string
       *   // pending/completed
       *   // [required]
       *   plan_status: string
       *   // [required]
       *   steps: Array<{
       *     // ID 编号
       *     // [required]
       *     id: string
       *     // 标题
       *     // [required]
       *     goal: string
       *     // 轮播动作列表
       *     // [required]
       *     actions: string[]
       *     // [required]
       *     current_action: string
       *     // [required]
       *     result: string
       *     // [required]
       *     photo: string
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *   }> | null
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // success/failed/running/pending/paused/completed
       *   // [required]
       *   status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *   // [required]
       *   error: string
       *   // [required]
       *   result: string
       *   // common/quick
       *   // [required]
       *   type: 'common' | 'quick'
       *   // [required]
       *   start_time: number
       *   // [required]
       *   quick_task_id: string | null
       *   // [required]
       *   device_id: string | null
       * }
       * ```
       */
      post_v1_tasks<
        Config extends Alova2MethodConfig<Task> & {
          data: {
            /**
             * [required]
             */
            description: string;
            /**
             * [required]
             */
            command: string;
            /**
             * [required]
             */
            plan: string;
            /**
             * [required]
             */
            steps: Array<{
              /**
               * ID 编号
               * [required]
               */
              id: string;
              /**
               * 标题
               * [required]
               */
              goal: string;
              /**
               * 轮播动作列表
               * [required]
               */
              actions: string[];
              /**
               * [required]
               */
              current_action: string;
              /**
               * [required]
               */
              result: string;
              /**
               * [required]
               */
              photo: string;
              /**
               * success/failed/running/pending/paused
               * [required]
               */
              status: 'pending' | 'running' | 'success' | 'failed' | 'paused';
            }> | null;
            /**
             * once/daily/monthly
             * [required]
             */
            schedule_type: ScheduleType;
            /**
             * success/failed/running/pending/paused/completed
             * [required]
             */
            status: 'pending' | 'running' | 'success' | 'failed' | 'paused';
            /**
             * [required]
             */
            result: string;
            /**
             * common/quick
             * [required]
             */
            type: 'common' | 'quick';
            /**
             * [required]
             */
            start_time: number;
            /**
             * [required]
             */
            quick_task_id: string | null;
            /**
             * [required]
             */
            device_id: string | null;
          };
        }
      >(
        config: Config
      ): Alova2Method<Task, 'general.post_v1_tasks', Config>;
      /**
       * ---
       *
       * [GET] 获取所有任务信息
       *
       * **path:** /v1/tasks
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   // [required]
       *   query: string
       *   // min：1
       *   // [required]
       *   page_size: number
       *   // min：1
       *   // [required]
       *   current_page: number
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   result?: Array<{
       *     // [required]
       *     id: string
       *     // [required]
       *     description: string
       *     // [required]
       *     command: string
       *     // [required]
       *     plan: string
       *     // pending/completed
       *     // [required]
       *     plan_status: string
       *     // [required]
       *     steps: Array<{
       *       // ID 编号
       *       // [required]
       *       id: string
       *       // 标题
       *       // [required]
       *       goal: string
       *       // 轮播动作列表
       *       // [required]
       *       actions: string[]
       *       // [required]
       *       current_action: string
       *       // [required]
       *       result: string
       *       // [required]
       *       photo: string
       *       // success/failed/running/pending/paused/completed
       *       // [required]
       *       status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     }> | null
       *     // once/daily/monthly
       *     // [required]
       *     schedule_type: 'once' | 'daily' | 'monthly'
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *     // [required]
       *     error: string
       *     // [required]
       *     result: string
       *     // common/quick
       *     // [required]
       *     type: 'common' | 'quick'
       *     // [required]
       *     start_time: number
       *     // [required]
       *     quick_task_id: string | null
       *     // [required]
       *     device_id: string | null
       *   }>
       * }
       * ```
       */
      get_v1_tasks<
        Config extends Alova2MethodConfig<{
          result?: Task[];
        }> & {
          params: {
            /**
             * [required]
             */
            query: string;
            /**
             * min：1
             * [required]
             */
            page_size: number;
            /**
             * min：1
             * [required]
             */
            current_page: number;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          result?: Task[];
        },
        'general.get_v1_tasks',
        Config
      >;
      /**
       * ---
       *
       * [GET] 查询任务信息SSE
       *
       * **path:** /v1/tasks_stream/{id}
       *
       * ---
       *
       * **Path Parameters**
       * ```ts
       * type PathParameters = {
       *   // [required]
       *   id: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   id: string
       *   // [required]
       *   description: string
       *   // [required]
       *   command: string
       *   // [required]
       *   plan: string
       *   // pending/completed
       *   // [required]
       *   plan_status: string
       *   // [required]
       *   steps: Array<{
       *     // ID 编号
       *     // [required]
       *     id: string
       *     // 标题
       *     // [required]
       *     goal: string
       *     // 轮播动作列表
       *     // [required]
       *     actions: string[]
       *     // [required]
       *     current_action: string
       *     // [required]
       *     result: string
       *     // [required]
       *     photo: string
       *     // success/failed/running/pending/paused/completed
       *     // [required]
       *     status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *   }> | null
       *   // once/daily/monthly
       *   // [required]
       *   schedule_type: 'once' | 'daily' | 'monthly'
       *   // success/failed/running/pending/paused/completed
       *   // [required]
       *   status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed'
       *   // [required]
       *   error: string
       *   // [required]
       *   result: string
       *   // common/quick
       *   // [required]
       *   type: 'common' | 'quick'
       *   // [required]
       *   start_time: number
       *   // [required]
       *   quick_task_id: string | null
       *   // [required]
       *   device_id: string | null
       * }
       * ```
       */
      get_v1_tasks_stream_id<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          id: string;
          /**
           * [required]
           */
          description: string;
          /**
           * [required]
           */
          command: string;
          /**
           * [required]
           */
          plan: string;
          /**
           * pending/completed
           * [required]
           */
          plan_status: string;
          /**
           * [required]
           */
          steps: Array<{
            /**
             * ID 编号
             * [required]
             */
            id: string;
            /**
             * 标题
             * [required]
             */
            goal: string;
            /**
             * 轮播动作列表
             * [required]
             */
            actions: string[];
            /**
             * [required]
             */
            current_action: string;
            /**
             * [required]
             */
            result: string;
            /**
             * [required]
             */
            photo: string;
            /**
             * success/failed/running/pending/paused/completed
             * [required]
             */
            status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          }> | null;
          /**
           * once/daily/monthly
           * [required]
           */
          schedule_type: ScheduleType;
          /**
           * success/failed/running/pending/paused/completed
           * [required]
           */
          status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          /**
           * [required]
           */
          error: string;
          /**
           * [required]
           */
          result: string;
          /**
           * common/quick
           * [required]
           */
          type: 'common' | 'quick';
          /**
           * [required]
           */
          start_time: number;
          /**
           * [required]
           */
          quick_task_id: string | null;
          /**
           * [required]
           */
          device_id: string | null;
        }> & {
          pathParams: {
            /**
             * [required]
             */
            id: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          id: string;
          /**
           * [required]
           */
          description: string;
          /**
           * [required]
           */
          command: string;
          /**
           * [required]
           */
          plan: string;
          /**
           * pending/completed
           * [required]
           */
          plan_status: string;
          /**
           * [required]
           */
          steps: Array<{
            /**
             * ID 编号
             * [required]
             */
            id: string;
            /**
             * 标题
             * [required]
             */
            goal: string;
            /**
             * 轮播动作列表
             * [required]
             */
            actions: string[];
            /**
             * [required]
             */
            current_action: string;
            /**
             * [required]
             */
            result: string;
            /**
             * [required]
             */
            photo: string;
            /**
             * success/failed/running/pending/paused/completed
             * [required]
             */
            status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          }> | null;
          /**
           * once/daily/monthly
           * [required]
           */
          schedule_type: ScheduleType;
          /**
           * success/failed/running/pending/paused/completed
           * [required]
           */
          status: 'pending' | 'running' | 'success' | 'failed' | 'paused' | 'completed';
          /**
           * [required]
           */
          error: string;
          /**
           * [required]
           */
          result: string;
          /**
           * common/quick
           * [required]
           */
          type: 'common' | 'quick';
          /**
           * [required]
           */
          start_time: number;
          /**
           * [required]
           */
          quick_task_id: string | null;
          /**
           * [required]
           */
          device_id: string | null;
        },
        'general.get_v1_tasks_stream_id',
        Config
      >;
      /**
       * ---
       *
       * [POST] 获取验证码
       *
       * **path:** /v1/auth/verify-code
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 手机号码或邮箱
       *   // [required]
       *   target: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = string
       * ```
       */
      post_v1_auth_verify_code<
        Config extends Alova2MethodConfig<string> & {
          data: {
            /**
             * 手机号码或邮箱
             * [required]
             */
            target: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<string, 'general.post_v1_auth_verify_code', Config>;
      /**
       * ---
       *
       * [POST] 手机注册
       *
       * **path:** /v1/auth/register/phone
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   phone: string
       *   // [required]
       *   password: string
       *   // [required]
       *   verify_code: string
       *   // 是否同意协议
       *   // [required]
       *   agree_terms: boolean
       *   // [required]
       *   nickname: string
       *   // [required]
       *   voice_broadcast: boolean
       *   // [required]
       *   no_confirm_mode: boolean
       *   // [required]
       *   ux_improve_plan: boolean
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = string
       * ```
       */
      post_v1_auth_register_phone<
        Config extends Alova2MethodConfig<string> & {
          data: {
            /**
             * [required]
             */
            phone: string;
            /**
             * [required]
             */
            password: string;
            /**
             * [required]
             */
            verify_code: string;
            /**
             * 是否同意协议
             * [required]
             */
            agree_terms: boolean;
            /**
             * [required]
             */
            nickname: string;
            /**
             * [required]
             */
            voice_broadcast: boolean;
            /**
             * [required]
             */
            no_confirm_mode: boolean;
            /**
             * [required]
             */
            ux_improve_plan: boolean;
          };
        }
      >(
        config: Config
      ): Alova2Method<string, 'general.post_v1_auth_register_phone', Config>;
      /**
       * ---
       *
       * [POST] 登陆
       *
       * **path:** /v1/auth/login
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   account: string
       *   // [required]
       *   password: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   token: string
       *   // [required]
       *   refresh_token: string
       *   // [required]
       *   user: {
       *     // [required]
       *     id: string
       *     create_time?: number
       *     last_modify_time?: number
       *     username?: string
       *     nickname?: string
       *     email?: string
       *     // [required]
       *     phone: string
       *     // 用户头像url
       *     avatar?: string
       *     status?: number
       *     last_login?: null
       *     verified_at?: null
       *     full_name?: string
       *     agree_terms?: boolean
       *     created_at?: string
       *     updated_at?: string
       *     // 取值：light
       *     ui_theme?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *     // 是否设置密码
       *     is_set_password?: boolean
       *     // 是否绑定手机
       *     phone_number_verified?: boolean
       *     // 用户名
       *     user_name?: string
       *     // 是否绑定邮箱
       *     email_verified?: boolean
       *   }
       * }
       * ```
       */
      post_v1_auth_login<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * [required]
           */
          user: User;
        }> & {
          data: {
            /**
             * [required]
             */
            account: string;
            /**
             * [required]
             */
            password: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * [required]
           */
          user: User;
        },
        'general.post_v1_auth_login',
        Config
      >;
      /**
       * ---
       *
       * [GET] 获取个人资料
       *
       * **path:** /v1/user/me
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   id: string
       *   create_time?: number
       *   last_modify_time?: number
       *   username?: string
       *   nickname?: string
       *   email?: string
       *   // [required]
       *   phone: string
       *   // 用户头像url
       *   avatar?: string
       *   status?: number
       *   last_login?: null
       *   verified_at?: null
       *   full_name?: string
       *   agree_terms?: boolean
       *   created_at?: string
       *   updated_at?: string
       *   // 取值：light
       *   ui_theme?: string
       *   // 取值：on,off
       *   ux_improve_plan?: string
       *   // 是否设置密码
       *   is_set_password?: boolean
       *   // 是否绑定手机
       *   phone_number_verified?: boolean
       *   // 用户名
       *   user_name?: string
       *   // 是否绑定邮箱
       *   email_verified?: boolean
       * }
       * ```
       */
      get_v1_user_me<Config extends Alova2MethodConfig<User>>(
        config?: Config
      ): Alova2Method<User, 'general.get_v1_user_me', Config>;
      /**
       * ---
       *
       * [POST] 手机验证码直接登陆
       *
       * **path:** /v1/auth/login/phone-code
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // [required]
       *   phone: string
       *   // [required]
       *   verify_code: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   token: string
       *   // [required]
       *   refresh_token: string
       *   // [required]
       *   user: {
       *     // [required]
       *     id: string
       *     create_time?: number
       *     last_modify_time?: number
       *     username?: string
       *     nickname?: string
       *     email?: string
       *     // [required]
       *     phone: string
       *     // 用户头像url
       *     avatar?: string
       *     status?: number
       *     last_login?: null
       *     verified_at?: null
       *     full_name?: string
       *     agree_terms?: boolean
       *     created_at?: string
       *     updated_at?: string
       *     // 取值：light
       *     ui_theme?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *     // 是否设置密码
       *     is_set_password?: boolean
       *     // 是否绑定手机
       *     phone_number_verified?: boolean
       *     // 用户名
       *     user_name?: string
       *     // 是否绑定邮箱
       *     email_verified?: boolean
       *   }
       * }
       * ```
       */
      post_v1_auth_login_phone_code<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * [required]
           */
          user: User;
        }> & {
          data: {
            /**
             * [required]
             */
            phone: string;
            /**
             * [required]
             */
            verify_code: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * [required]
           */
          user: User;
        },
        'general.post_v1_auth_login_phone_code',
        Config
      >;
      /**
       * ---
       *
       * [GET] 通过refresh token刷新access token
       *
       * **path:** /v1/auth/refresh-token
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   access_token: string
       *   // [required]
       *   id_token: string
       *   // [required]
       *   refresh_token: string
       *   // [title] 过期时间戳
       *   // [required]
       *   expires_in: string
       * }
       * ```
       */
      get_v1_auth_refresh_token<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          access_token: string;
          /**
           * [required]
           */
          id_token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * 过期时间戳
           * ---
           * [required]
           */
          expires_in: string;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          access_token: string;
          /**
           * [required]
           */
          id_token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * 过期时间戳
           * ---
           * [required]
           */
          expires_in: string;
        },
        'general.get_v1_auth_refresh_token',
        Config
      >;
      /**
       * ---
       *
       * [GET] 登出
       *
       * **path:** /v1/auth/logout
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = string
       * ```
       */
      get_v1_auth_logout<Config extends Alova2MethodConfig<string>>(
        config?: Config
      ): Alova2Method<string, 'general.get_v1_auth_logout', Config>;
      /**
       * ---
       *
       * [PUT] 更新用户配置
       *
       * **path:** /v1/user/profile
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   ui_theme?: string
       *   ux_improve_plan?: string
       *   nickname?: string
       *   user_name?: string
       *   // 用户头像url
       *   avatar_url?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   id: string
       *   create_time?: number
       *   last_modify_time?: number
       *   username?: string
       *   nickname?: string
       *   email?: string
       *   // [required]
       *   phone: string
       *   // 用户头像url
       *   avatar?: string
       *   status?: number
       *   last_login?: null
       *   verified_at?: null
       *   full_name?: string
       *   agree_terms?: boolean
       *   created_at?: string
       *   updated_at?: string
       *   // 取值：light
       *   ui_theme?: string
       *   // 取值：on,off
       *   ux_improve_plan?: string
       *   // 是否设置密码
       *   is_set_password?: boolean
       *   // 是否绑定手机
       *   phone_number_verified?: boolean
       *   // 用户名
       *   user_name?: string
       *   // 是否绑定邮箱
       *   email_verified?: boolean
       * }
       * ```
       */
      put_v1_user_profile<
        Config extends Alova2MethodConfig<User> & {
          data: {
            ui_theme?: string;
            ux_improve_plan?: string;
            nickname?: string;
            user_name?: string;
            /**
             * 用户头像url
             */
            avatar_url?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<User, 'general.put_v1_user_profile', Config>;
      /**
       * ---
       *
       * [GET] 获取登录跳转连接
       *
       * **path:** /v1/auth/login-url
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [title] 登录成功跳转回调地址
       *   // [required]
       *   url: string
       *   // [title] 状态
       *   // [required]
       *   state: string
       *   // [required]
       *   nonce: {
       *     // [required]
       *     id: string
       *     create_time?: number
       *     last_modify_time?: number
       *     username?: string
       *     nickname?: string
       *     email?: string
       *     // [required]
       *     phone: string
       *     // 用户头像url
       *     avatar?: string
       *     status?: number
       *     last_login?: null
       *     verified_at?: null
       *     full_name?: string
       *     agree_terms?: boolean
       *     created_at?: string
       *     updated_at?: string
       *     // 取值：light
       *     ui_theme?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *     // 是否设置密码
       *     is_set_password?: boolean
       *     // 是否绑定手机
       *     phone_number_verified?: boolean
       *     // 用户名
       *     user_name?: string
       *     // 是否绑定邮箱
       *     email_verified?: boolean
       *   }
       * }
       * ```
       */
      get_v1_auth_login_url<
        Config extends Alova2MethodConfig<{
          /**
           * 登录成功跳转回调地址
           * ---
           * [required]
           */
          url: string;
          /**
           * 状态
           * ---
           * [required]
           */
          state: string;
          /**
           * [required]
           */
          nonce: User;
        }>
      >(
        config?: Config
      ): Alova2Method<
        {
          /**
           * 登录成功跳转回调地址
           * ---
           * [required]
           */
          url: string;
          /**
           * 状态
           * ---
           * [required]
           */
          state: string;
          /**
           * [required]
           */
          nonce: User;
        },
        'general.get_v1_auth_login_url',
        Config
      >;
      /**
       * ---
       *
       * [GET] OIDC登陆
       *
       * **path:** /v1/auth/oidc/callback
       *
       * ---
       *
       * **Query Parameters**
       * ```ts
       * type QueryParameters = {
       *   code?: string
       *   state?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [required]
       *   access_token: string
       *   // [required]
       *   refresh_token: string
       *   // [required]
       *   id_token: string
       *   // [required]
       *   user: {
       *     // [required]
       *     id: string
       *     create_time?: number
       *     last_modify_time?: number
       *     username?: string
       *     nickname?: string
       *     email?: string
       *     // [required]
       *     phone: string
       *     // 用户头像url
       *     avatar?: string
       *     status?: number
       *     last_login?: null
       *     verified_at?: null
       *     full_name?: string
       *     agree_terms?: boolean
       *     created_at?: string
       *     updated_at?: string
       *     // 取值：light
       *     ui_theme?: string
       *     // 取值：on,off
       *     ux_improve_plan?: string
       *     // 是否设置密码
       *     is_set_password?: boolean
       *     // 是否绑定手机
       *     phone_number_verified?: boolean
       *     // 用户名
       *     user_name?: string
       *     // 是否绑定邮箱
       *     email_verified?: boolean
       *   }
       *   // [title] 过期时间戳
       *   // [required]
       *   expires_in: number
       * }
       * ```
       */
      get_v1_auth_oidc_callback<
        Config extends Alova2MethodConfig<{
          /**
           * [required]
           */
          access_token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * [required]
           */
          id_token: string;
          /**
           * [required]
           */
          user: User;
          /**
           * 过期时间戳
           * ---
           * [required]
           */
          expires_in: number;
        }> & {
          params: {
            code?: string;
            state?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * [required]
           */
          access_token: string;
          /**
           * [required]
           */
          refresh_token: string;
          /**
           * [required]
           */
          id_token: string;
          /**
           * [required]
           */
          user: User;
          /**
           * 过期时间戳
           * ---
           * [required]
           */
          expires_in: number;
        },
        'general.get_v1_auth_oidc_callback',
        Config
      >;
      /**
       * ---
       *
       * [POST] OIDC登出
       *
       * **path:** /v1/auth/logout-url
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 退出后的回调url
       *   redirect_uri?: string
       *   // 撤销id_token
       *   // [required]
       *   id_token: string
       *   state?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // [title] 跳转url
       *   // [required]
       *   url: string
       * }
       * ```
       */
      post_v1_auth_logout_url<
        Config extends Alova2MethodConfig<{
          /**
           * 跳转url
           * ---
           * [required]
           */
          url: string;
        }> & {
          data: {
            /**
             * 退出后的回调url
             */
            redirect_uri?: string;
            /**
             * 撤销id_token
             * [required]
             */
            id_token: string;
            state?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 跳转url
           * ---
           * [required]
           */
          url: string;
        },
        'general.post_v1_auth_logout_url',
        Config
      >;
      /**
       * ---
       *
       * [POST] 设置/更改密码
       *
       * **path:** /v1/user/set-password
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 手机号
       *   // [required]
       *   phone: string
       *   // 验证码
       *   // [required]
       *   verify_code: string
       *   // 密码
       *   // [required]
       *   password: string
       *   // 用户名（用于登录，必须唯一）
       *   user_name?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      post_v1_user_set_password<
        Config extends Alova2MethodConfig<unknown> & {
          data: {
            /**
             * 手机号
             * [required]
             */
            phone: string;
            /**
             * 验证码
             * [required]
             */
            verify_code: string;
            /**
             * 密码
             * [required]
             */
            password: string;
            /**
             * 用户名（用于登录，必须唯一）
             */
            user_name?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.post_v1_user_set_password', Config>;
      /**
       * ---
       *
       * [POST] 绑定新手机号
       *
       * **path:** /v1/user/reset-phone
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 新手机号
       *   // [required]
       *   phone: string
       *   // 新验证码
       *   // [required]
       *   verify_code: string
       *   // 重置手机号token
       *   // [required]
       *   reset_phone_token: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = unknown
       * ```
       */
      post_v1_user_reset_phone<
        Config extends Alova2MethodConfig<unknown> & {
          data: {
            /**
             * 新手机号
             * [required]
             */
            phone: string;
            /**
             * 新验证码
             * [required]
             */
            verify_code: string;
            /**
             * 重置手机号token
             * [required]
             */
            reset_phone_token: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<unknown, 'general.post_v1_user_reset_phone', Config>;
      /**
       * ---
       *
       * [POST] 获取绑定手机号的token
       *
       * **path:** /v1/user/reset-phone/token
       *
       * ---
       *
       * **RequestBody**
       * ```ts
       * type RequestBody = {
       *   // 旧手机号验证码
       *   // [required]
       *   old_verify_code: string
       *   // 旧手机号
       *   old_phone?: string
       * }
       * ```
       *
       * ---
       *
       * **Response**
       * ```ts
       * type Response = {
       *   // 绑定手机号token
       *   // [required]
       *   reset_phone_token: string
       * }
       * ```
       */
      post_v1_user_reset_phone_token<
        Config extends Alova2MethodConfig<{
          /**
           * 绑定手机号token
           * [required]
           */
          reset_phone_token: string;
        }> & {
          data: {
            /**
             * 旧手机号验证码
             * [required]
             */
            old_verify_code: string;
            /**
             * 旧手机号
             */
            old_phone?: string;
          };
        }
      >(
        config: Config
      ): Alova2Method<
        {
          /**
           * 绑定手机号token
           * [required]
           */
          reset_phone_token: string;
        },
        'general.post_v1_user_reset_phone_token',
        Config
      >;
    };
  }

  var TuriXApi: TuriXApi;
}
