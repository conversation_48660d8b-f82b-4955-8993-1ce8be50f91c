import type { withConfigType } from './createApis'

type ConfigMap = Parameters<typeof withConfigType>[0]

export const userConfigMap = {
  get_v1_auth_login_url: {
    meta: {
      authRole: null,
    },
  },
  get_v1_auth_oidc_callback: {
    meta: {
      authRole: 'login',
    },
  },
  get_v1_auth_refresh_token: {
    meta: {
      authRole: 'refreshToken',
    },
  },
  post_v1_auth_login: {
    meta: {
      authRole: 'login',
    },
  },
  post_v1_auth_login_phone_code: {
    meta: {
      authRole: 'login',
    },
  },
  post_v1_auth_logout_url: {
    meta: {
      authRole: 'logout',
    },
  },
  post_v1_auth_verify_code: {
    meta: {
      authRole: 'assignToken',
    },
  },
} as {
  [K in keyof ConfigMap extends `general.${infer T}` ? T : never]: ConfigMap[`general.${K}`]
}
