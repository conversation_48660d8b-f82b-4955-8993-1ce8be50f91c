/// <reference types='./globals.d.ts' />
/* tslint:disable */
/* eslint-disable */
/**
 * AgenticAI-Cloud - version 1.0.0
 *
 *
 *
 * OpenAPI version: 3.0.1
 *
 *
 * NOTE: This file is auto generated by the alova's vscode plugin.
 *
 * https://alova.js.org/devtools/vscode
 *
 * **Do not edit the file manually.**
 */
export default {
  'general.get_v1_quick_tasks_id': ['GET', '/v1/quick_tasks/{id}'],
  'general.put_v1_quick_tasks_id': ['PUT', '/v1/quick_tasks/{id}'],
  'general.delete_v1_quick_tasks_id': ['DELETE', '/v1/quick_tasks/{id}'],
  'general.post_v1_quick_tasks': ['POST', '/v1/quick_tasks'],
  'general.get_v1_quick_tasks': ['GET', '/v1/quick_tasks'],
  'general.get_v1_chat_histories_id': ['GET', '/v1/chat_histories/{id}'],
  'general.put_v1_chat_histories_id': ['PUT', '/v1/chat_histories/{id}'],
  'general.delete_v1_chat_histories_id': ['DELETE', '/v1/chat_histories/{id}'],
  'general.post_v1_chat_histories': ['POST', '/v1/chat_histories'],
  'general.get_v1_chat_histories': ['GET', '/v1/chat_histories'],
  'general.get_v1_devices_code': ['GET', '/v1/devices/{code}'],
  'general.delete_v1_devices_code': ['DELETE', '/v1/devices/{code}'],
  'general.put_v1_devices_code': ['PUT', '/v1/devices/{code}'],
  'general.get_v1_devices': ['GET', '/v1/devices'],
  'general.get_v1_devices_invoke_code': ['GET', '/v1/devices/invoke/{code}'],
  'general.get_v1_devices_perm_invoke_code': ['GET', '/v1/devices/perm/invoke/{code}'],
  'general.get_v1_tasks_id': ['GET', '/v1/tasks/{id}'],
  'general.put_v1_tasks_id': ['PUT', '/v1/tasks/{id}'],
  'general.delete_v1_tasks_id': ['DELETE', '/v1/tasks/{id}'],
  'general.post_v1_tasks': ['POST', '/v1/tasks'],
  'general.get_v1_tasks': ['GET', '/v1/tasks'],
  'general.get_v1_tasks_stream_id': ['GET', '/v1/tasks_stream/{id}'],
  'general.post_v1_auth_verify_code': ['POST', '/v1/auth/verify-code'],
  'general.post_v1_auth_register_phone': ['POST', '/v1/auth/register/phone'],
  'general.post_v1_auth_login': ['POST', '/v1/auth/login'],
  'general.get_v1_user_me': ['GET', '/v1/user/me'],
  'general.post_v1_auth_login_phone_code': ['POST', '/v1/auth/login/phone-code'],
  'general.get_v1_auth_refresh_token': ['GET', '/v1/auth/refresh-token'],
  'general.get_v1_auth_logout': ['GET', '/v1/auth/logout'],
  'general.put_v1_user_profile': ['PUT', '/v1/user/profile'],
  'general.get_v1_auth_login_url': ['GET', '/v1/auth/login-url'],
  'general.get_v1_auth_oidc_callback': ['GET', '/v1/auth/oidc/callback'],
  'general.post_v1_auth_logout_url': ['POST', '/v1/auth/logout-url'],
  'general.post_v1_user_set_password': ['POST', '/v1/user/set-password'],
  'general.post_v1_user_reset_phone': ['POST', '/v1/user/reset-phone'],
  'general.post_v1_user_reset_phone_token': ['POST', '/v1/user/reset-phone/token']
};
