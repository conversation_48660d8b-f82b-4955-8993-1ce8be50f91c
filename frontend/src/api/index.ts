import type { AxiosError } from 'axios'
import { axiosRequestAdapter } from '@alova/adapter-axios'
import { createAlova } from 'alova'
import { createClientTokenAuthentication } from 'alova/client'
import vueHook from 'alova/vue'
import { findKey, mapKeys } from 'es-toolkit'
import { router } from '@/router'
import ApiDef from './apiDefinitions'
import { userConfigMap } from './config'
import { createApis, withConfigType } from './createApis'

const accessToken = useLocalStorage('access_token', '')
const idToken = useLocalStorage('id_token', '')
const refreshToken = useLocalStorage('refresh_token', '')
const expireTime = useLocalStorage('expire_time', Date.now() + 24 * 3600 * 1000)

// @ts-expect-error meta 为可选的对象
const refreshTokenMethod = findKey(userConfigMap, o => o?.meta?.authRole === 'refreshToken')

function initToken(access: string, refash: string, id: string, expire: number) {
  accessToken.value = access
  refreshToken.value = refash
  idToken.value = id
  expireTime.value = expire
}

const { onAuthRequired, onResponseRefreshToken } = createClientTokenAuthentication<typeof vueHook, typeof axiosRequestAdapter>({
  assignToken: (method) => {
    method.config.headers.Authorization = `Bearer ${accessToken.value}`
  },
  login({ data }) {
    initToken(data.data.access_token, data.data.refresh_token, data.data.id_token, data.data.expire_time)
  },
  logout() {
    localStorage.clear()
  },
  refreshToken: refreshTokenMethod && {
    // 当token过期时触发，在此函数中触发刷新token
    handler: async () => {
      // @ts-expect-error 由用户自行确保调用的一定是refreshTokenMethod
      const { access_token, expire_time, id_token, refresh_token } = await $api[refreshTokenMethod]({
        headers: {
          'Refresh-Token': refreshToken.value,
        },
      })
      initToken(access_token, refresh_token, id_token, expire_time)
    },

    // 在请求前触发，将接收到method参数，并返回boolean表示token是否过期
    isExpired: () => {
      return expireTime.value < Date.now()
    },
  },
})

export const alovaInstance = createAlova({
  baseURL: import.meta.env.VITE_API_BaseUrl,
  beforeRequest: onAuthRequired((method) => {

  }),
  cacheFor: null,
  requestAdapter: axiosRequestAdapter(),
  responded: onResponseRefreshToken({
    onError: async (err: AxiosError<Sys.ReturnTemplate>, method) => {
      if (err.status === 401) {
        $message.error('请先登录')

        router.push('/auth/login')
      }
      // else if (err.status === 403) {
      //   $message.error('客户端不在线')
      // }
      // // 其他统一提示处理
      // else {
      // }
      $message.error(err.response?.data.message ?? err.message)
      return Promise.reject(err)
    },
    onSuccess: async ({ data }, method) => {
      $message.success(data.message)
      return data.data
    },
  }),
  statesHook: vueHook,
  timeout: import.meta.env.VITE_API_Timeout,
})

export const $$userConfigMap = withConfigType(Object.assign({}, ApiDef, mapKeys(userConfigMap, (_, k) => `general.${k}`) as Parameters<typeof withConfigType>[0]))

const Apis = createApis(alovaInstance, $$userConfigMap)

export default Apis.general
