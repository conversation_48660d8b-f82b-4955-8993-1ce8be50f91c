import type { AlovaGenerics, Method } from 'alova'
import { type AlovaMethodHandler, type RequestHookConfig, type UseHookExposure, useRequest, useWatcher, type WatcherHookConfig } from 'alova/client'

export function $req<AG extends AlovaGenerics, <PERSON>rg<PERSON> extends unknown[]>(
  methodHandler: Method<AG> | AlovaMethodHandler<AG, Args>,
  config?: RequestHookConfig<AG, Args>
): UseHookExposure<AG, Args>
export function $req<AG extends AlovaGenerics, Args extends unknown[] = unknown[]>(
  methodHandler: Method<AG> | AlovaMethodHandler<AG, Args>,
  watchingStates: AG['StatesExport']['Watched'][],
  config?: WatcherHookConfig<AG, Args>
): UseHookExposure<AG, Args>
export function $req<AG extends AlovaGenerics, Args extends unknown[] = unknown[]>(
  methodHandler: Method<AG> | AlovaMethodHandler<AG, Args>,
  option?: AG['StatesExport']['Watched'][] | RequestHookConfig<AG, Args>,
  config?: WatcherHookConfig<AG, Args>,
) {
  if (Array.isArray(option)) {
    return useWatcher(methodHandler, option, config)
  }
  return useRequest(methodHandler, option)
}
