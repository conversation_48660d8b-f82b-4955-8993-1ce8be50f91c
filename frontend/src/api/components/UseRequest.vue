<script setup lang="ts" generic="AG extends AlovaGenerics, <PERSON><PERSON><PERSON> extends unknown[] = unknown[]">
import type { AlovaGenerics, Method } from 'alova'
import { type AlovaMethodHandler, useRequest, useWatcher, type WatcherHookConfig } from 'alova/client'

const { config, method, showError = true, showLoading = true, sync = false, watched } = defineProps<{
  method: Method<AG> | AlovaMethodHandler<AG, Args>
  watched?: AG['StatesExport']['Watched'][]
  config?: WatcherHookConfig<AG, Args>
  showLoading?: boolean
  showError?: boolean
  sync?: boolean
}>()

const exports = reactive(watched ? useWatcher(method, watched, config) : useRequest(method, config))

defineExpose(exports)
</script>

<template>
  <div class="size-full relative overflow-auto">
    <slot v-if="showError && exports.error" name="error" v-bind="exports">
      <page-error v-if="exports.error.isAxiosError" :error="exports.error" />
    </slot>
    <slot v-if="!(exports.error || (sync && exports.data === undefined))" v-bind="exports" />
    <p-block-u-i v-if="showLoading && exports.loading" blocked class="absolute inset-0">
      <slot name="loading" v-bind="exports">
        <page-loading />
      </slot>
    </p-block-u-i>
  </div>
</template>
