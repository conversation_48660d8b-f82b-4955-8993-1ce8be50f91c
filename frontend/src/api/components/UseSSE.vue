<script setup lang="ts" generic="AG extends AlovaGenerics, Data = unknown, <PERSON>rgs extends unknown[] = unknown[]">
import type { AlovaGenerics, Method } from 'alova'
import type { AlovaMethodHandler, SSEHookConfig } from 'alova/client'
import { useSSE } from 'alova/client'

const { config, method } = defineProps<{
  method: Method<AG> | AlovaMethodHandler<AG, Args>,
  config?: SSEHookConfig
}>()

const exports = reactive(useSSE(method, config))

defineExpose(exports)
</script>

<template>
  <slot v-bind="exports" />
</template>
