<script setup lang="ts" generic="AG extends AlovaGenerics, ListData extends unknown[], <PERSON><PERSON><PERSON> extends unknown[]">
import type { AlovaGenerics, Method } from 'alova'
import type { PaginationHookConfig } from 'alova/client'
import { usePagination } from 'alova/client'

const { config, method, showError = true, showLoading = true } = defineProps<{
  method: (page: number, pageSize: number, ...args: Args) => Method<AG>
  config?: PaginationHookConfig<AG, ListData>
  showLoading?: boolean
  showError?: boolean
}>()

const exports = reactive(usePagination(method, config))

defineExpose(exports)
</script>

<template>
  <div class="size-full relative overflow-auto">
    <slot v-if="showError && exports.error" name="error" v-bind="exports">
      <page-error v-if="exports.error.isAxiosError" :error="exports.error" />
    </slot>
    <slot v-if="!exports.error" v-bind="exports" />
    <p-block-u-i v-if="showLoading && exports.loading" blocked class="absolute inset-0">
      <slot name="loading" v-bind="exports">
        <page-loading />
      </slot>
    </p-block-u-i>
  </div>
</template>
