<script setup lang="tsx">
import { mapValues } from 'es-toolkit'
import { messageMap } from '@/stores/message'

function render() {
  return (
    <div class="flex flex-col items-center fixed w-full z-9999 gap-4 top-4 h-0 overflow-visible" v-auto-animate>
      {Array.from(messageMap.entries()).reverse().map(([key, message]) => (
        <p-message class="max-w-4/5 w-max" key={key} {...message.props} onLifeEnd={message.destroy} onClose={message.destroy} onMouseover={message.pause} onMouseleave={message.resume}>
          {
            mapValues(message.slots, vnode => typeof vnode === 'function' ? vnode : () => vnode)
          }
        </p-message>
      ))}
    </div>
  )
}
</script>

<template>
  <render />
  <slot />
</template>
