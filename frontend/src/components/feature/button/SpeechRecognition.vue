<script setup lang="ts">
const emit = defineEmits<{
  final: [string]
  input: [string]
}>()

const {
  isFinal,
  isListening,
  isSupported,
  result,
  stop,
  toggle,
} = useSpeechRecognition({
  lang: 'zh-CN',
})

whenever(isFinal, () => {
  emit('final', result.value)
  stop()
  result.value = ''
})

watchEffect(() => {
  emit('input', result.value)
})
</script>

<template>
  <p-button
    :icon="isListening ? 'icon-[svg-spinners--bars-scale-middle]' : 'icon-sys-icon-voice'"
    @click="toggle()"
  />
</template>
