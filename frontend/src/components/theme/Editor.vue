<script setup lang="ts">
import { useTheme } from '@/stores/theme'

const colorModes = Object.freeze([{ label: $i18n.t('dark-mode'), value: 'dark' }, { label: $i18n.t('light-mode'), value: 'light' }, { label: $i18n.t('system-mode'), value: 'auto' }] as const)

const theme = useTheme()

const primaryFilterText = ref('')
const surfaceFilterText = ref('')

const primaryCollapsed = ref(false)
const surfaceCollapsed = ref(false)
</script>

<template>
  <div class="grid grid-cols-[auto_1fr] items-center gap-4 [&>*:nth-child(odd)]:text-end">
    <div> {{ `${$t('color-mode')}:` }} </div>
    <p-select-button
      v-model="theme.colorMode"
      :allow-empty="false"
      option-label="label"
      option-value="value"
      :options="[...colorModes]"
    />
    <div> {{ `${$t('style')}:` }} </div>
    <p-select-button
      v-model="theme.preset"
      :allow-empty="false"
      :options="theme.presets"
    />
    <div> {{ `${$t('ripple')}:` }} </div>
    <p-toggle-switch v-model="theme.ripple" @update:model-value="$primevue.config.ripple = theme.ripple = $event" />
    <div class="size-full">
      <div class="p-[var(--p-panel-toggleable-header-padding)] h-[var(--p-button-icon-only-width)] px-0 box-content content-center">
        {{ $t('primary-color') }}:
      </div>
    </div>

    <p-panel v-model:collapsed="primaryCollapsed" toggleable>
      <template #header>
        <div class="flex gap-4 items-center">
          <theme-color-item v-bind="theme.primary" />
          <p-icon-field :class="[primaryCollapsed ? 'animate-out' : 'animate-in']" class="hover:*:last:block hover:*:last:animate-in fade-in fade-out fill-mode-forwards">
            <p-input-icon class="icon-[lucide--search]" />
            <p-input-text v-model="primaryFilterText" :placeholder="$t('color-name')" />
            <p-input-icon class="icon-[line-md--close] starting:hidden fade-in slide-in-from-bottom animate-out fade-out slide-out-to-bottom fill-mode-forwards" @click="primaryFilterText = ''" />
          </p-icon-field>
        </div>
      </template>
      <theme-color v-model="theme.primary" :filter-text="primaryFilterText" />
    </p-panel>
    <div class="size-full">
      <div class="p-[var(--p-panel-toggleable-header-padding)] h-[var(--p-button-icon-only-width)] px-0 box-content content-center">
        {{ $t('surface-color') }}:
      </div>
    </div>

    <p-panel v-model:collapsed="surfaceCollapsed" toggleable>
      <template #header>
        <div class="flex gap-4 items-center">
          <theme-color-item v-bind="theme.surface" />
          <p-icon-field v-show="!surfaceCollapsed" class="hover:*:last:block hover:*:last:animate-in">
            <p-input-icon class="icon-[lucide--search]" />
            <p-input-text v-model="surfaceFilterText" :placeholder="$t('color-name')" />
            <p-input-icon class="icon-[line-md--close] starting:hidden fade-in slide-in-from-bottom animate-out fade-out slide-out-to-bottom fill-mode-forwards" @click="surfaceFilterText = ''" />
          </p-icon-field>
        </div>
      </template>
      <theme-color v-model="theme.surface" :filter-text="surfaceFilterText" />
    </p-panel>
  </div>
</template>
