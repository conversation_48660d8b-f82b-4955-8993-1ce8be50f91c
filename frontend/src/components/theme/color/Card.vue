<script setup lang="ts">
import type { ColorSheet } from '../Color.vue'
import colorsea from 'colorsea'

const { rainbow } = defineProps<ColorSheet>()

defineEmits<{
  submit: [ColorSheet]
}>()

const rgb = computed(() => colorsea(rainbow[500]).rgb())

const hex = computed(() => colorsea(rainbow[500]).hex())
</script>

<template>
  <p-card class="border border-surface">
    <template #title>
      <div class="flex gap-2 items-center ">
        <div class="size-[1em]" :style="{ backgroundColor: rainbow[500] }" />
        <div class="flex items-end gap-2">
          {{ name }}
          <div class="text-[0.5em]">
            {{ hex }}
          </div>
        </div>
      </div>
    </template>
    <template #content>
      <div class="grid grid-cols-[auto_1fr] items-center gap-4">
        <div>{{ $t('ribbon') }}</div>
        <theme-color-item v-bind="$props" :has-name="false" />
        <div>rgb</div>
        <p-input-group class="w-44">
          <p-input-number :model-value="rgb[0]" pt:pc-input-text:root:class="text-center" />
          <p-input-number :model-value="rgb[1]" pt:pc-input-text:root:class="text-center" />
          <p-input-number :model-value="rgb[2]" pt:pc-input-text:root:class="text-center" />
        </p-input-group>
      </div>
    </template>
    <template #footer>
      <p-button class="w-full" size="small" @click="$emit('submit', { name, rainbow })">
        {{ $t('apply') }}
      </p-button>
    </template>
  </p-card>
</template>
