<script setup lang="ts">
import type { ColorSheet } from '../Color.vue'
import colorsea from 'colorsea'

const { rainbow } = defineProps<ColorSheet>()

const cmyk = computed(() => colorsea(rainbow[500]).cmyk())

const rgb = computed(() => colorsea(rainbow[500]).rgb())
</script>

<template>
  <p-card>
    <template #header>
      <theme-color-item v-bind="$props" class="p-4" />
    </template>

    <template #content>
      <div class="size-full flex justify-between items-center [&>*]:flex-1 [&>*]:w-0 gap-4">
        <div class="flex flex-col ">
          <p-divider />
          C
          <p-knob :model-value="cmyk[0]" :stroke-width="5" :size="50" />
          <p-divider />
          M
          <p-knob :model-value="cmyk[1]" :stroke-width="5" :size="50" />
          <p-divider />
          Y
          <p-knob :model-value="cmyk[2]" :stroke-width="5" :size="50" />
          <p-divider />
          K
          <p-knob :model-value="cmyk[3]" :stroke-width="5" :size="50" />
          <p-divider />
        </div>
        <div class="grid grid-cols-3 grid-rows-[auto_1fr] size-full justify-items-center">
          <div>R</div>
          <div>G</div>
          <div>B</div>
          <div class="w-2 bg-[#F00] transition-[height]" :style="{ height: `${rgb[0] * 100 / 255}%` }" />
          <div class="w-2 bg-[#0F0] transition-[height]" :style="{ height: `${rgb[1] * 100 / 255}%` }" />
          <div class="w-2 bg-[#00F] transition-[height]" :style="{ height: `${rgb[2] * 100 / 255}%` }" />
        </div>
        <div class="tracking-[0.25em] [writing-mode:vertical-lr] text-7xl">
          {{ name }}
        </div>
      </div>
    </template>
  </p-card>
</template>
