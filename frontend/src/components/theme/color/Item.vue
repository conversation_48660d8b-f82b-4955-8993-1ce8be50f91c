<script setup lang="ts">
import type { ColorSheet } from '../Color.vue'

const { hasName = true, rainbow } = defineProps<ColorSheet & { hasName?: boolean }>()

const w = computed(() => 100 / Object.keys(rainbow).length)

const style = computed<Vue.CSSProperties>(() => ({
  backgroundImage: `linear-gradient(to right, ${Object.values(rainbow).reduce((pre, cur, i) => {
    pre.push(`${cur} ${i * w.value}%`, `${cur} ${(i + 1) * w.value}%`)
    return pre
  }, [] as string[]).join(',')})`,
}))
</script>

<template>
  <div class="flex gap-4 items-center justify-between">
    <slot v-if="hasName" name="name">
      <div class="w-20">
        {{ name }}
      </div>
    </slot>

    <div
      :style="style"
      class="w-44 h-4"
    />
  </div>
</template>
