<script setup lang="ts">
import { palette } from '@primeuix/themes'
import chinese from 'colorsea/colors/chinese'
import { useTheme } from '@/stores/theme'

export interface ColorSheet {
  name: string
  rainbow: Primevue.Themes.PaletteDesignToken
}

const modelValue = defineModel<ColorSheet>({ required: true })

const { filterText } = defineProps<{ filterText?: string }>()

const theme = useTheme()

const chineseColor = computed(() => Object.entries(chinese).filter(([name]) => name.includes(filterText ?? '')).map(([name, color]) => ({ name, rainbow: palette(color) })))

const viewItems = reactive(new Set<ColorSheet>())

watchDebounced(() => filterText, () => {
  viewItems.clear()
  nextTick(() => {
    if (chineseColor.value.at(0)) {
      viewItems.add(chineseColor.value.at(0)!)
    }
  })
}, {
  debounce: 200,
  immediate: true,
})

const systemColor = computed(() => theme.presetColor.filter(([name]) => name.includes(filterText ?? '')))

const hoverColor = ref<ColorSheet>()

const sccordionValue = ref(['system-color'])
</script>

<template>
  <use-element-size v-slot="{ height }" class="flex gap-4 justify-center xl:w-320 lg:w-250 w-170" @mouseleave="hoverColor = undefined">
    <div :style="{ height: `${height}px` }" class="overflow-y-auto flex-1">
      <p-accordion multiple :value="sccordionValue">
        <p-accordion-panel value="system-color">
          <p-accordion-header class="sticky top-0 z-10">
            {{ $t('system-color') }}
          </p-accordion-header>
          <p-accordion-content>
            <div v-auto-animate class="grid items-start xl:grid-cols-3 lg:grid-cols-2 px-4 gap-8 justify-around">
              <theme-color-card
                v-for="[name, rainbow] in systemColor"
                :key="name"
                :name="name"
                :rainbow="rainbow"
                class="size-fit animate-in fade-in ease-in hover:border-primary"
                @mouseover="hoverColor = { name, rainbow }"
                @submit="modelValue = $event"
              />
            </div>
          </p-accordion-content>
        </p-accordion-panel>
        <p-accordion-panel value="chinese-color">
          <p-accordion-header class="sticky top-0 z-10">
            {{ $t('chinese-color') }}
          </p-accordion-header>
          <p-accordion-content>
            <div
              v-auto-animate
              class="grid items-start xl:grid-cols-3 lg:grid-cols-2 px-4 gap-8 justify-around"
            >
              <theme-color-card
                v-for="{ name, rainbow }, i in viewItems"
                :key="name"
                v-element-visibility="[visible => visible && chineseColor[i + 1] && viewItems.add(chineseColor[i + 1]), { once: false }]"
                :name="name"
                :rainbow="rainbow"
                class="size-fit animate-in fade-in ease-in hover:border-primary"
                @mouseover="hoverColor = { name, rainbow }"
                @submit="modelValue = $event"
              />
            </div>
          </p-accordion-content>
        </p-accordion-panel>
      </p-accordion>
    </div>

    <theme-color-preview v-bind="hoverColor ?? modelValue" class="h-auto" />
  </use-element-size>
</template>
