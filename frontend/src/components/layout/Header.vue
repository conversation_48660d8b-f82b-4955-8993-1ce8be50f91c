<script setup lang="ts">
const collapse = defineModel<boolean>({ default: false })
</script>

<template>
  <div class="flex gap-2 p-2 items-center">
    <p-button
      variant="text"
      icon="icon-[iconoir--sidebar-collapse]"
      class="transition-[margin]"
      :class="collapse ? 'ml-0' : '-ml-12 mr-4'"
      @click="collapse = !collapse"
    />
    <layout-header-title />
    <layout-header-main class="flex-1" />
    <layout-header-info />
  </div>
</template>
