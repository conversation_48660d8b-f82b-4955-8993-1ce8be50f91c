<script setup lang="ts">
const i18n = useI18n()

const { language } = useNavigatorLanguage()

const locale = useLocalStorage('language', (language.value && i18n.availableLocales.includes(language.value)) ? language.value : i18n.locale.value)

watchEffect(() => {
  i18n.locale.value = locale.value
})

const options = i18n.availableLocales.map(locale => ({
  label: i18n.t(locale, {}, { locale }),
  value: locale,
}))
</script>

<template>
  <div class="flex gap-2 items-center">
    {{ $t('language') }}
    <p-select
      v-model="locale"
      :options="options"
      option-label="label"
      option-value="value"
      size="small"
    />
  </div>
</template>
