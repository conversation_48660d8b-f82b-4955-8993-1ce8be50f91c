<script setup lang="ts">
import { useTheme } from '@/stores/theme'

const theme = useTheme()

const icon = computed(() => {
  switch (theme.colorMode) {
    case 'dark':
      return 'icon-[material-symbols--dark-mode-rounded]'
    case 'light':
      return 'icon-[material-symbols--light-mode-rounded]'
    default: return 'icon-[material-symbols--brightness-6]'
  }
})

const colorList = ['dark', 'light', 'auto'] as const
</script>

<template>
  <div class="flex gap-4 items-center">
    <layout-header-info-language />
    <layout-header-info-theme />
    <p-button
      class="size-8"
      :icon="icon"
      severity="secondary"
      @click="theme.colorMode = colorList.at((colorList.findIndex(color => color === theme.colorMode) + 1) % 3)!"
    />
  </div>
</template>
