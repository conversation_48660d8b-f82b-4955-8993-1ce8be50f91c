import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useCookies } from '@vueuse/integrations/useCookies'

const { get } = useCookies()
const abortController = new AbortController()
export async function fetchSSE(url: string, callback: (data: ApiData.Task, abortController: AbortController, type: string) => void) {
  await fetchEventSource(url, {
    // body: JSON.stringify({
    //   // 请求体数据
    // }),
    headers: {
      'Authorization': `Bear<PERSON> ${get('access_token')}`,
      'Cache-Control': 'no-cache',
    },
    method: 'get', // 可以指定请求方法
    onclose() {
      // 连接关闭时调用
    },
    onerror(err) {
      // 发生错误时调用
      throw err
    },
    onmessage(event) {
      // 接收到消息时调用
      const res = JSON.parse(event.data)
      callback(res.data, abortController, event.event)
    },
    async onopen(response) {
      // 连接建立时调用
      if (response.ok) {
        // console.log('Connection established')
      }
      else {
        throw new Error('Failed to connect')
      }
    },
    signal: abortController.signal,
  })
}