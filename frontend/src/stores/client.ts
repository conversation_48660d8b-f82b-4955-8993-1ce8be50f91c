import { Events } from '@wailsio/runtime'

interface OnEvent {

}

export const InnerUIEnum = {

} as const

interface EmitEvent {

}

export const useClient = defineStore('client', () => {
  return {
    deviceID: localStorage.getItem('device-id'),
    emit<T extends keyof EmitEvent>(...arg: [T, ...EmitEvent[T][]]) {
      Events.Emit(new Events.WailsEvent(...arg))
    },
    isClient: true,
    on<T extends keyof OnEvent>(type: T, listener: (...args: OnEvent[T]) => void) {
      Events.On(type, (ev) => {
        listener(...(ev.data ?? []) as OnEvent[T])
      })
    },
  }
})
