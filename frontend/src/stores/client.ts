import { Events } from '@wailsio/runtime'

interface OnEvent {
  'event.toggleBall': [boolean]
  'event.runTask': [string]
  'event.mouseMoveGlobal': [{ x: number, y: number }]
  'event.stopMouseMoveGlobal': []
}

export const InnerUIEnum = {
  CloseFloat: 1,
  CloseMouseListener: 6,
  ExitChat: 4,
  Logout: 2,
  OpenBall: 0,
  OpenChat: 3,
  OpenMouseListener: 5,
} as const

interface EmitEvent {
  'event.inner.ui': [number]
  'event.floatBallChatMainWindow': [boolean]
  'event.mainWindow': [number]
}

export const useClient = defineStore('client', () => {
  return {
    deviceID: localStorage.getItem('device-id'),
    emit<T extends keyof EmitEvent>(...arg: [T, ...EmitEvent[T][]]) {
      Events.Emit(new Events.WailsEvent(...arg))
    },
    isClient: true,
    on<T extends keyof OnEvent>(type: T, listener: (...args: OnEvent[T]) => void) {
      Events.On(type, (ev) => {
        listener(...(ev.data ?? []) as OnEvent[T])
      })
    },
  }
})
