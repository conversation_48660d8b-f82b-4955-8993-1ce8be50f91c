import { isVNode } from 'vue'

export interface MessageOptions {
  keepAlive?: boolean
  duration?: number
  props?: Primevue.MessageProps & {
    [P in keyof HTMLElementEventMap as `on${Capitalize<P>}`]?: (ev: HTMLElementEventMap[P]) => void
  } & {
    [P in keyof Primevue.MessageEmitsOptions as `on${Capitalize<P>}`]?: Primevue.MessageEmitsOptions[P]
  }
  onDestroy?: (message: messageMapValue) => void
  slots: {
    [P in keyof Primevue.MessageSlots]?: Vue.VNodeChild | ((...args: Parameters<Primevue.MessageSlots[P]>) => Vue.VNodeChild);
  }
}

type messageMapValue = MessageOptions & Vue.Raw<VueUse.UseCountdownReturn> & { destroy: VueUse.Fn }
export const messageMap = reactive(new Map<symbol, messageMapValue>())

function create(options: MessageOptions) {
  const key = Symbol('message')
  const { on, trigger } = createEventHook<messageMapValue>()
  options.onDestroy && on(options.onDestroy)
  function destroy() {
    if (!messageMap.has(key)) {
      return
    }
    trigger(messageMap.get(key)!)
    messageMap.delete(key)
  }
  const countDown = useCountdown(() => options.duration ?? import.meta.env.VITE_MESSAGE_Duration, {
    onComplete: destroy,
  })
  countDown.start()

  messageMap.set(key, { keepAlive: import.meta.env.VITE_MESSAGE_KeepAlive, ...options, ...countDown, destroy })

  return {
    destroy,
    message: messageMap.get(key)!,
  }
}

export const $message = Object.freeze({
  create,
  destroyAll() {
    messageMap.clear()
  },
  ...Object.fromEntries(
    (['success', 'info', 'warn', 'error', 'secondary', 'contrast']).map(severity => [severity, (options: MessageOptions | Vue.VNodeChild | (() => Vue.VNodeChild)) => {
      if (options && typeof options === 'object' && !Array.isArray(options) && !isVNode(options)) {
        return create(reactiveComputed(() => ({ ...options, props: { severity, ...options.props } } as messageMapValue)))
      }

      return create(reactiveComputed(() => ({ props: { severity }, slots: { default: options as string } })))
    }]),
  ) as Record<'success' | 'info' | 'warn' | 'error' | 'secondary' | 'contrast', (options: MessageOptions | Vue.VNodeChild | (() => Vue.VNodeChild)) => ReturnType<typeof create>>,
})
