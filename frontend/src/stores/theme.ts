import type { Preset } from '@primeuix/themes/types'
import { definePreset, updatePrimaryPalette, updateSurfacePalette, usePreset } from '@primeuix/themes'
import { z } from 'zod/v4'

const colorSchema = z.object({
  50: z.string(),
  100: z.string(),
  200: z.string(),
  300: z.string(),
  400: z.string(),
  500: z.string(),
  600: z.string(),
  700: z.string(),
  800: z.string(),
  900: z.string(),
  950: z.string(),
})

const presets = {
  Aura: () => import('@primeuix/themes/aura'),
  Lara: () => import('@primeuix/themes/lara'),
  Material: () => import('@primeuix/themes/material'),
  Nora: () => import('@primeuix/themes/nora'),
} as const

if (!localStorage.getItem('preset-name')) {
  localStorage.setItem('preset-name', 'Aura')
  localStorage.setItem('preset', JSON.stringify((await presets.Aura()).default))
}

const presetName = useLocalStorage<keyof typeof presets>('preset-name', 'Aura')

const preset = useLocalStorage<Preset>('preset', {})

usePreset(preset.value)

const ripple = useLocalStorage('ripple', false)

const isDark = useDark()

const primary = useLocalStorage('color-primary', {
  name: 'emerald',
  rainbow: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
    950: '#022c22',
  },
})

const surface = useLocalStorage('color-surface', isDark.value
  ? { name: 'zinc', rainbow: {
      50: '#fafafa',
      100: '#f4f4f5',
      200: '#e4e4e7',
      300: '#d4d4d8',
      400: '#a1a1aa',
      500: '#71717a',
      600: '#52525b',
      700: '#3f3f46',
      800: '#27272a',
      900: '#18181b',
      950: '#09090b',
    } }
  : { name: 'slate', rainbow: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    } })

export const useTheme = defineStore('theme', () => {
  return {
    colorMode: useColorMode({ emitAuto: true }),
    preset: computed({
      get() {
        return presetName.value
      },
      async set(name) {
        presetName.value = name

        preset.value = usePreset(definePreset((await presets[name]()).default, {
          semantic: preset.value.semantic as object,
        }))
      },
    }),
    presetColor: computed(() => Object.entries(preset.value.primitive!).filter(([_, val]) => colorSchema.safeParse(val).success) as [string, Required<Primevue.Themes.PaletteDesignToken>][]),
    presets: computed(() => Object.keys(presets)),
    primary: computed({
      get() {
        return primary.value
      },
      set({ name, rainbow }) {
        primary.value = { name, rainbow }
        preset.value = updatePrimaryPalette(rainbow)
      },
    }),
    ripple: computed({
      get() {
        return ripple.value
      },
      set(bool) {
        ripple.value = bool
      },
    }),
    surface: computed({
      get() {
        return surface.value
      },
      set({ name, rainbow }) {
        surface.value = { name, rainbow }
        preset.value = updateSurfacePalette(isDark.value
          ? {
              // @ts-expect-error dark theme
              dark: surface.value.rainbow,
            }
          : {
              light: surface.value.rainbow,
            })
      },
    }),
  }
})
