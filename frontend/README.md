# 模板说明

**此模板采用传统手工编写，不含任何AI成分。**

## 全局函数、组件与指令

- `VueUse`提供的所有函数，参见[@vueuse/core](https://vueuse.org/functions.html)。
- `VueUse`提供的所有组件及指令，参见[@vueuse/components](https://vueuse.org/guide/components.html)。
- 所有的[primevue](https://primevue.org/button/)，使用`P`作为公共前缀，如`PButton`。
- `src/components`下的所有组件。组件名为其相对`src/components`的路径，如`src/components/page/404.vue`的组件名为`Page404`、`src/components/page/error/Axios.vue`的组件名为`PageErrorAxios`。
- `src/api/components`下的所有组件。组件名为其相对`src/api/components`的路径，如`src/api/components/UsePagination.vue`的组件名为`UsePagination`。
- 更多请参见[vite.config.ts](./vite.config.ts)中的[Components](https://github.com/unplugin/unplugin-vue-components#readme)。
- [\$message](./src/stores/message.ts)，该函数依赖[Message](./src/components/provider/Message.vue)环境，应保证系统有且只有一个`ProviderMessage`环境，推荐包裹在最外层组件上，如`<ProviderMessage><App/></ProviderMessage>`，存在多个`ProviderMessage`组件时，可能会导致setup内的表现与setup外的表现不一致。`$message`也可在环境变量中全局设置。
  - `VITE_MESSAGE_KeepAlive`：鼠标悬浮时不消失
  - `VITE_MESSAGE_Duration`：在显示此时间后自动消失
- [\$api](#api)
- [\$req](#req)

## 路由

此模板采用[基于文件的路由](https://uvr.esm.is/introduction.html)，位于`src/views/**/*.vue`的文件都将被编译为路由，请不要在此处编写复杂的业务逻辑。

已为每一级路由都自动生成了404路由，样式位于[404.vue](./src/components/page/404.vue)。如需调整具体某一级的404页面，请在该级路由手动添加`[...path].vue`文件，具体请参考[Catch all / 404 Not found route](https://uvr.esm.is/guide/file-based-routing.html#catch-all-404-not-found-route)。

## 主题

### tailwindcss4

关于`tailwindcss4`的配置请写在[main.css](./src/assets/main.css)

### icons

图标采用[iconify](https://iconify.design/docs/usage/css/tailwind/tailwind4/)方案，可在[yesicon](https://yesicon.app/)搜索全部可用图标。复制生成的css即可，如`<i class="icon-[material-symbols--360]"></i>`。

图标还支持[组件用法](https://github.com/unplugin/unplugin-icons#readme)，如

```html
<script setup>
  import MaterialSymbols360 from '~icons/material-symbols/360'
</script>

<template>
  <MaterialSymbols360 />
</template>
```

也可配合自动导入使用，但需要加上固定前缀`I`

```html
<i-material-symbols-360 />
```

所有位于`src/icons`下的svg图标都支持组件用法，其所属图标集为`app-icons`，假设存在文件`src/icons/logo.svg`，则使用方法如下：

```html
<script setup>
  import Logo from '~icons/app-icons/logo'
</script>

<template>
  <Logo />
</template>
```

也可配合自动导入使用，也需要加上固定前缀`I`

```html
<i-app-icons-logo />
```

图标还支持直接使用来自Figma的图标，需配置以下环境变量：

```bash
PLUGINS_IconSet_outPutPath = './src/assets/sys-icons.css'
PLUGINS_IconSet_depth = 3 # https://iconify.design/docs/libraries/tools/import/figma/#depth
PLUGINS_IconSet_file = '' # https://iconify.design/docs/libraries/tools/import/figma/file-id.html
PLUGINS_IconSet_pages = [] # https://iconify.design/docs/libraries/tools/import/figma/#pages
PLUGINS_IconSet_token = '' # Figma API token, 推荐在env.local中配置，#https://iconify.design/docs/libraries/tools/import/figma/token.html
PLUGINS_IconSet_prefix = '\$sys' # 仅识别Figma中以`$sys`开头的图标

```

在图标更新后，需要手动调用`npm run dev:plugin:create-icon-set`或`npm run build:plugin:create-icon-set`来生成新的css文件。

> 仅node v24以上可以直接运行ts文件，低版本node请先安装ts-node，如在bun、deno等环境中运行，需手动修改`package.json`中的运行命令。

从Figma中导入的图标目前仅支持在css中使用，生成的图标以`icon-sys`开头，如`<i class="icon-sys-apple"></i>`。

> 如有需要后续可以支持组件用法，以及直接导出svg图标到项目。

在使用Figma设计图标时，需注意图标的颜色，使其可以由css控制，参考[Changing icon palette](https://iconify.design/docs/articles/cleaning-up-icons/palette.html#changing-icon-palette)。

iconify将图标分为两类：

1. [Icons with palette](https://iconify.design/docs/articles/cleaning-up-icons/palette.html#palette)，这些图标将作为背景使用，其颜色无法更改。使用时须在图标后面带上`-bg`，如`<i class="icon-sys-apple-bg"></i>`。
2. [Monotone icons](https://iconify.design/docs/articles/cleaning-up-icons/palette.html#monotone)，大多数图标都是单色的，它们只有一种颜色，而且可以更改。使用css的`color`属性来更改颜色，如`<i class="icon-sys-apple text-[#f00]"></i>`。

### primevue主题

> 此部份还不完善

TODO：

- 调用[useTheme](./src/stores/theme.ts)应用或设置主题，也可使用[ThemeEditor](./src/components/theme/Editor.vue)进行可视化的设置。
- 使用[usePreset](https://primevue.org/theming/styled/#usepreset)应用主题，可在`src/themes/*`下预设多个主题，然后使用`usePreset`应用主题。

## 接口调用

接口调用采用的请求库为 [alova](https://alova.js.org/zh-CN/tutorial/getting-started/introduce)。

### 类型化

接口的类型由 [@alova/wormhole](https://alova.js.org/zh-CN/api/wormhole/) 提供，推荐搭配vscode使用，请参考 [编辑器扩展集成](https://alova.js.org/zh-CN/tutorial/getting-started/extension-integration)，配置文件路径为 [alova.config.ts](./alova.config.ts)。

生成的类型在 [env.d.ts](./types/env.d.ts) 中二次包装为 `Sys.Api`，同时提供工具类型`Sys.ApiParameters`、`SysApiReturn`用于提取参数或返回值类型。

### 使用说明

#### 配置

- 公共url：[VITE_API_BaseUrl](./env/.env)
- 超时：[VITE_API_Timeout](./env/.env)
- 针对具体请求方法的集中配置：[userConfigMap](./src/api/config.ts)

#### $api

- 用于调用接口，它提供了类型化参数与返回值，以及自动处理jwt相关的登录、登出、刷新等操作（如果需要），并能自动处理一些错误信息。
- 由[AutoImport](https://github.com/unplugin/unplugin-icons#readme)暴露在全局，无需导入即可全局使用，也挂载到了vue实例上，可直接在模版中使用。
- 类型签名为`Sys.Api`
- 示例：`$api.get_user({ params: { id: 12}})`

#### $req

`$req`是对[客户端请求策略](#客户端请求策略)的再封装，他会根据传入的参数自动选择调用[useRequest](https://alova.js.org/zh-CN/tutorial/client/strategy/use-request)或是[useWatcher](https://alova.js.org/zh-CN/tutorial/client/strategy/use-watcher)

#### 客户端请求策略

此策略与vue强耦合，最常用的有以下三个函数：

- [useRequest](https://alova.js.org/zh-CN/tutorial/client/strategy/use-request)
- [useWatcher](https://alova.js.org/zh-CN/tutorial/client/strategy/use-watcher)
- [usePagination](https://alova.js.org/zh-CN/tutorial/client/strategy/use-pagination)

对于`useRequest`与`useWatcher`，有进一步二次封装的函数[$req](#req)，以及其对应的组件用法[UseRequest](./src/api/components/UseRequest.vue)。

`usePagination`也有其组件用法[UsePagination](./src/api/components/UsePagination.vue)。
