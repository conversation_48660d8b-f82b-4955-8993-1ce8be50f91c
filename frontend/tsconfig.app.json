{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ESNext", "jsx": "preserve", "lib": ["esnext", "dom"], "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "unplugin-icons/types/vue", "unplugin-vue-router/client", "@intlify/unplugin-vue-i18n/messages"], "allowImportingTsExtensions": true, "erasableSyntaxOnly": true}, "include": ["types/**/*", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"]}