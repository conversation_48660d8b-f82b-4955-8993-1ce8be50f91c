/// <reference types="vite/client" />
/* eslint-disable perfectionist/sort-exports */

import 'vue-router'
import 'vue'
import 'type-fest'
// import '@vueuse/components'
import '@vueuse/core'
import '@vueuse/head'
import '@vueuse/math'
import '@vueuse/components'
import 'primevue'
import '@primeuix/themes/types'
import 'vue-component-type-helpers'
import '@/api/globals'

declare global {
  declare namespace VueUse {
    // export type * from '@vueuse/components'
    export type * from '@vueuse/core'
    export type * from '@vueuse/head'
    export type * from '@vueuse/math'
    export type * from '@vueuse/components'
  }

  declare namespace Vue {
    export type * from 'vue'
    export type * from 'vue-component-type-helpers'
  }

  declare namespace Primevue {
    export * from 'primevue'
    export namespace Themes {
      export * from '@primeuix/themes/types'
      import type { PaletteDesignToken as PT } from '@primeuix/themes/types'

      export interface PaletteDesignToken extends Required<PT> {
        0?: string
      }
    }
  }

  declare namespace VueRouter {
    export type * from 'vue-router'
  }
  declare namespace TypeFest {
    export type * from 'type-fest'
  }
  declare namespace TuriX {
    export type Api = TuriXApi['general']
    declare namespace Api {
      export type * from '@/api/globals'
    }
    export interface ReturnTemplate {
      data: unknown
      message: string
    }
    export type ApiParameters<Url extends (keyof TuriX.Api.UserMethodConfigMap extends `general.${infer P}` ? P : never)> = Parameters<TuriX.Api[Url]>

    export type ApiReturn<Url extends (keyof TuriX.Api.UserMethodConfigMap extends `general.${infer P}` ? P : never)> = Awaited<ReturnType<TuriX.Api[Url]>>
  }

}

declare module '@primeuix/themes' {
  export function palette(color: string): Primevue.Themes.PaletteDesignToken

}

declare module 'vue-router' {
  interface RouteMeta {

  }
}

type Directives = {
  [P in keyof Vue.GlobalDirectives]?: Vue.GlobalDirectives[P] extends Vue.ObjectDirective<infer _, infer V> ? V : never;
}

declare module 'vue' {

  interface ComponentCustomProperties {
    $api: typeof import('@/api/index')['default']
    // $R: typeof import('vue-hooks-plus')['useRequest']
  }

  type a = keyof GlobalDirectives

  interface HTMLAttributes extends Directives, AttributifyAttributes {
  }

}

import 'alova'

declare module 'alova' {
  export interface AlovaCustomTypes {
    // meta: {
    //   role: string
    // }
  }
}
