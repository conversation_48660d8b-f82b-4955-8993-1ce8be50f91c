/* eslint-disable no-console */
import type { FigmaImportOptions } from '@iconify/tools/lib/import/figma/types/options.js'
import { argv } from 'node:process'
import { importFromFigma } from '@iconify/tools'
import {
  cleanupSVG,
  deOptimisePaths,
  isEmptyColor,
  parseColors,
  runSVGO,
} from '@iconify/tools'
import { getIconCSS, getIconsCSS } from '@iconify/utils'
import { compareColors, stringToColor } from '@iconify/utils/lib/colors'
import chalk from 'chalk'
import { outputFileSync } from 'fs-extra/esm'
import { loadEnv } from 'vite'

interface IconSetOptions extends FigmaImportOptions {
  outputPath: string
}
export async function createIconSet({ outputPath, ...options }: IconSetOptions) {
  try {
    const result = await importFromFigma(options)

    // @ts-expect-error 文档未作修改时返回字符串
    if (result === 'not_modified') {
      console.log(chalk.green('图标集未修改，无需重新生成'))
      return
    }

    const { iconSet, version } = result

    let css = ''

    await iconSet.forEach((name, type) => {
      if (type !== 'icon') {
        // Do not parse aliases
        return
      }

      // Get SVG instance for icon
      const svg = iconSet.toSVG(name)!

      // Clean up and validate icon
      // This will throw an exception if icon is invalid
      cleanupSVG(svg)
      runSVGO(svg)

      css += getIconCSS(svg.getIcon(), {
        iconSelector: `@utility icon-sys-${name}-bg`,
        mode: 'background',
      })

      parseColors(svg, {
        callback: (attr, colorStr, color) => {
          if (!color) {
            // Color cannot be parsed!
            throw new Error(`Invalid color: "${colorStr}" in attribute ${attr}`)
          }

          if (isEmptyColor(color)) {
            // Color is empty: 'none' or 'transparent'. Return as is
            return color
          }

          if (attr === 'fill') {
            console.log(chalk.yellow(`图标${chalk.blue.bold(name)}已被修改：属性${chalk.blue.bold(attr)}的值${chalk.blue.bold(colorStr)}已被修改为${chalk.green.bold('currentColor')}`))
            return 'currentColor'
          }

          // Remove shapes with white color
          // if (compareColors(color, whiteColor)) {
          //   console.log(chalk.yellow(`图标${chalk.blue.bold(name)}已被修改：属性${chalk.blue.bold(attr)}的值${chalk.blue.bold(colorStr)}已被删除`))
          //   return 'remove'
          // }
          return color

          // throw new Error(`Unexpected color "${colorStr}" in attribute ${attr}`)
        },
        defaultColor: 'currentColor',
      })

      css += getIconCSS(svg.getIcon(), {
        iconSelector: `@utility icon-sys-${name}`,
        mode: 'mask',
      })
    })

    outputFileSync(outputPath, css, 'utf8')

    console.log(`

      ${chalk.blue('成功生成图标数：')} ${chalk.green(Object.keys(iconSet.entries).length)}
      ${chalk.blue('figma 版本：')} ${chalk.green(version)}

    `)
  }
  catch (error) {
    console.error(chalk.red('生成图标集失败:'), error)
  }
}

const mode = argv.includes('--mode') ? argv[argv.indexOf('--mode') + 1] : 'development'

const env = loadEnv(mode, './env', '')
createIconSet({
  // cacheDir: './plugins/.cache/icons-set',
  depth: +env.PLUGINS_IconSet_depth,
  file: env.PLUGINS_IconSet_file,
  // filterParentNode: (node, document) => {
  //   console.log(node)
  //   return false
  // },
  iconNameForNode(node) {
  // console.log(node.type)
    if (node.name.startsWith(env.PLUGINS_IconSet_prefix)) {
      return node.name.replace(`${env.PLUGINS_IconSet_prefix}-`, '')
    }
    return null
  },
  ifModifiedSince: true,
  outputPath: env.PLUGINS_IconSet_outPutPath,
  // eslint-disable-next-line no-new-func
  pages: new Function(`return ${env.PLUGINS_IconSet_pages}`)(),
  prefix: 'sys',
  token: env.PLUGINS_IconSet_token,
})
