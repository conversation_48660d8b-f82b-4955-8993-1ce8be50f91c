package main

import (
	"AgenticAI-Client/internal/app"
	"AgenticAI-Client/pkg/selfupdate/launcher"
	"embed"
)

// Wails uses Go's `embed` package to embed the frontend files into the binary.
// Any files in the frontend/dist folder will be embedded into the binary and
// made available to the frontend.
// See https://pkg.go.dev/embed for more information.

//go:embed all:frontend/dist/*
var assets embed.FS

// main function serves as the application's entry point. It initializes the application, creates a window,
// and starts a goroutine that emits a time-based event every second. It subsequently runs the application and
// logs any error that might occur.
func main() {
	launcher.Start(func() {
		app.New(assets).Run()
	})
}
